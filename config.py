import os
from datetime import timedelta

class Config:
    """基础配置类"""
    # 密钥配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or os.urandom(24)

    # 会话配置
    PERMANENT_SESSION_LIFETIME = timedelta(hours=1)

    # 数据库配置
    DATABASE_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data', 'database.db')

    # 文件上传配置
    UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')
    MAX_CONTENT_LENGTH = 10 * 1024 * 10240  # 100MB
    ALLOWED_EXTENSIONS = {'txt', 'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'zip', 'rar', 'jpg', 'jpeg', 'png', 'gif'}

    # MediaMTX配置
    MEDIAMTX_HOST = os.environ.get('MEDIAMTX_HOST', 'localhost')
    MEDIAMTX_RTSP_PORT = int(os.environ.get('MEDIAMTX_RTSP_PORT', 8554))
    MEDIAMTX_HLS_PORT = int(os.environ.get('MEDIAMTX_HLS_PORT', 8888))
    MEDIAMTX_CONFIG_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'mediamtx', 'mediamtx.yml')
    MEDIAMTX_BIN_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'mediamtx', 'mediamtx')

    # 桌面捕获配置
    DESKTOP_STREAM_NAME = 'desktop'
    FFMPEG_PATH = 'ffmpeg'  # 假设ffmpeg在PATH中
    
    # 确保必要的目录存在
    @staticmethod
    def init_app(app):
        # 创建数据目录
        data_dir = os.path.dirname(Config.DATABASE_PATH)
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)

        # 创建上传目录
        if not os.path.exists(Config.UPLOAD_FOLDER):
            os.makedirs(Config.UPLOAD_FOLDER)

        # 创建MediaMTX目录
        mediamtx_dir = os.path.dirname(Config.MEDIAMTX_CONFIG_PATH)
        if not os.path.exists(mediamtx_dir):
            os.makedirs(mediamtx_dir)

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False

class TestingConfig(Config):
    """测试环境配置"""
    TESTING = True
    DATABASE_PATH = ':memory:'  # 使用内存数据库进行测试

# 配置字典
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
