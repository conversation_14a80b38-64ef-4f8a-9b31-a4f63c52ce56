// 全局变量，用于倒计时功能
let countdownInterval;
let examEndTime;
let examTimeInMinutes = 0;
let timerDisplay;
let timerStatus;

// 清除之前的倒计时（如果有）
function clearCountdown() {
    if (!timerDisplay) timerDisplay = document.getElementById("timer-display");
    if (!timerStatus) timerStatus = document.getElementById("timer-status");
    
    if (countdownInterval) {
        clearInterval(countdownInterval);
        countdownInterval = null;
    }
    timerDisplay.textContent = "--:--:--";
    timerStatus.textContent = "未开始";
    timerStatus.className = "layui-badge layui-bg-gray";
}

// 开始倒计时
function startCountdown(minutes) {
    if (!timerDisplay) timerDisplay = document.getElementById("timer-display");
    if (!timerStatus) timerStatus = document.getElementById("timer-status");
    
    clearCountdown();
    
    examTimeInMinutes = minutes;
    const now = new Date();
    examEndTime = new Date(now.getTime() + minutes * 60 * 1000);
    
    timerStatus.textContent = "考试中";
    timerStatus.className = "layui-badge layui-bg-green";
    
    updateCountdown();
    countdownInterval = setInterval(updateCountdown, 1000);
}

// 更新倒计时显示
function updateCountdown() {
    if (!timerDisplay) timerDisplay = document.getElementById("timer-display");
    if (!timerStatus) timerStatus = document.getElementById("timer-status");
    
    const now = new Date();
    const timeDiff = examEndTime - now;
    
    if (timeDiff <= 0) {
        // 时间到，自动提交
        clearInterval(countdownInterval);
        timerDisplay.textContent = "00:00:00";
        timerStatus.textContent = "时间到";
        timerStatus.className = "layui-badge layui-bg-red";
        
        // 自动提交答案
        document.getElementById("submitBtn").click();
        return;
    }
    
    // 计算剩余时间
    const hours = Math.floor(timeDiff / (1000 * 60 * 60));
    const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);
    
    // 格式化显示
    timerDisplay.textContent = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    
    // 更新进度条
    const totalTime = examTimeInMinutes * 60 * 1000;
    const progress = ((totalTime - timeDiff) / totalTime * 100).toFixed(1);
    const progressBar = document.getElementById('countdown-timer');
    progressBar.setAttribute('lay-percent', progress + '%');
    layui.element.progress('countdown-timer', progress + '%');
    
    // 当剩余时间少于5分钟时，显示警告
    if (timeDiff <= 5 * 60 * 1000) {
        timerStatus.textContent = "即将结束";
        timerStatus.className = "layui-badge layui-bg-orange";
    }
}

document.addEventListener("DOMContentLoaded", () => {
    const examSelect = document.getElementById("examSelect");
    const questionsDiv = document.getElementById("examQuestions");
    timerDisplay = document.getElementById("timer-display");
    timerStatus = document.getElementById("timer-status");

    examSelect.addEventListener("change", async (e) => {
        const examId = e.target.value;
        if (!examId) {
            questionsDiv.innerHTML = "<p>请选择试卷。</p>";
            clearCountdown();
            return;
        }

        try {
            const response = await fetch(`/student/exam/${examId}`);
            const exam = await response.json();
            if (!exam) {
                questionsDiv.innerHTML = "<p>试卷加载失败。</p>";
                clearCountdown();
                return;
            }
            
            // 开始倒计时
            startCountdown(exam.exam_time || 45); // 使用试卷设置的时间，默认45分钟

            questionsDiv.innerHTML = "";
            const typeCount = {};  // 记录题型数量
            const questionAnchors = [];  // 记录题目锚点

            exam.questions.forEach((q, index) => {
                // 为每个题目添加锚点
                const questionDiv = document.createElement("div");
                questionDiv.className = "question";
                questionDiv.id = `question-${index + 1}`;
                
                // 更新题型统计
                typeCount[q.type] = (typeCount[q.type] || 0) + 1;
                questionAnchors.push({type: q.type, number: index + 1});
                
                questionDiv.innerHTML = `  
                    <h3>${index + 1}. ${q.desc}（${q.score}分）</h3>  
                    <input type="hidden" name="question_id" value="${q.id}">  
                `;

                if (q.type === "single" || q.type === "multiple") {
                    const optionsHtml = q.options.map((opt, i) => {
                        const label = String.fromCharCode(65 + i); // 生成字母标识 A、B、C...
                        return `  
                            <div class="layui-form-item">
                                <input type="${q.type === 'single' ? 'radio' : 'checkbox'}" 
                                       name="${q.type === 'single' ? 'radio_' : 'checkbox_'}${q.id}" 
                                       value="${label}" 
                                       data-question="${q.id}" 
                                       title="${label}. ${opt}" 
                                       lay-skin="primary">
                            </div>
                        `;
                    }).join("");
                    questionDiv.innerHTML += `  
                        <div class="layui-form">  
                            ${optionsHtml}  
                        </div>  
                    `;
                } else if (q.type === "judge") {
                    const optionsHtml = ["正确", "错误"].map((opt, i) => {
                        const label = String.fromCharCode(65 + i); // 生成字母标识 A、B...
                        return `  
                            <div class="layui-form-item">
                                <input type="radio" 
                                       name="radio_${q.id}" 
                                       value="${label}" 
                                       data-question="${q.id}" 
                                       title="${label}. ${opt}">
                            </div>
                        `;
                    }).join("");
                    questionDiv.innerHTML += `  
                        <div class="layui-form">  
                            ${optionsHtml}  
                        </div>  
                    `;
                } else if (q.type === "fill") {
                    questionDiv.innerHTML += `  
                        <div class="layui-form-item">  
                            <input type="text" 
                                   name="text_${q.id}" 
                                   placeholder="请填写答案" 
                                   data-question="${q.id}" 
                                   class="layui-input">  
                        </div>  
                    `;
                } else if (q.type === "subjective") {
                    questionDiv.innerHTML += `  
                        <div class="layui-form-item layui-form-text">  
                            <div class="layui-input-block" style="margin-left: 0;">
                                <textarea name="textarea_${q.id}" 
                                          placeholder="请输入您的答案" 
                                          data-question="${q.id}" 
                                          class="layui-textarea"></textarea>  
                            </div>
                        </div>  
                    `;
                }

                questionsDiv.appendChild(questionDiv);
            });

            // 生成题型导航
            const typeNav = document.getElementById("typeNavigation");
            typeNav.innerHTML = `
                ${Object.entries(typeCount)
                    .filter(([type]) => ["single", "multiple", "judge", "fill", "subjective"].includes(type))
                    .map(([type, count]) => `
                        <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="location.href='#question-${findFirstOfType(type)}'">
                            ${getTypeName(type)} (${count}题)
                        </button>
                    `).join("")
                }
            `;

            // 生成题目导航
            const questionNav = document.getElementById("questionNavigation");
            let questionButtons = '';
            
            questionAnchors.forEach((q, i) => {
                questionButtons += `
                    <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" 
                            onclick="location.href='#question-${i + 1}'" 
                            style="margin: 2px;">
                        ${i + 1}
                    </button>`;
            });
            
            questionNav.innerHTML = questionButtons;

            // 辅助函数
            function getTypeName(type) {
                const names = {single: '单选', multiple: '多选', judge: '判断', fill: '填空', subjective: '主观'};
                return names[type] || type;
            }

            function findFirstOfType(type) {
                return questionAnchors.findIndex(q => q.type === type) + 1;
            }

        } catch (error) {
            console.error("Error:", error);
            questionsDiv.innerHTML = "<p>试卷加载失败，请检查网络或服务器。</p>";
        }
    });
    
    //提交答案
    document.getElementById("submitBtn").addEventListener("click", async () => {
        const studentId = document.getElementById("studentId").value.trim();
        // 检查学号是否为空
        if (!studentId) {
            alert("请输入您的学号！");
            return;
        }
        const examId = document.getElementById("examSelect").value;
        const answers = {};
        
        // 如果是由倒计时触发的自动提交，显示提示
        if (timerStatus.textContent === "时间到") {
            alert("考试时间已结束，系统将自动提交您的答案。");
        }

        // 收集所有题目的答案
        document.querySelectorAll('.layui-card').forEach(questionCard => {
            const questionIdInput = questionCard.querySelector('input[name="question_id"]');
            if (!questionIdInput) return;
            
            const questionId = questionIdInput.value;
            
            // 收集单选题答案
            const radio = questionCard.querySelector('input[type="radio"]:checked');
            if (radio) {
                answers[questionId] = radio.value;
            }
            
            // 收集多选题答案
            const checkboxes = questionCard.querySelectorAll('input[type="checkbox"]:checked');
            if (checkboxes.length > 0) {
                answers[questionId] = Array.from(checkboxes).map(cb => cb.value).join(',');
            }
            
            // 收集填空题答案
            const textInput = questionCard.querySelector('input[type="text"]');
            if (textInput && textInput.value.trim()) {
                answers[questionId] = textInput.value.trim();
            }
            
            // 收集主观题答案
            const textarea = questionCard.querySelector('textarea');
            if (textarea && textarea.value.trim()) {
                answers[questionId] = textarea.value.trim();
            }
        });

        // 确保每个问题都有答案记录
        document.querySelectorAll('.question').forEach(question => {
            const questionId = question.querySelector('input[name="question_id"]').value;
            if (!answers[questionId]) {
                answers[questionId] = ""; // 如果没有选择或填写，提交空字符串
            }
        });

        console.log("提交答案:", answers);

        const data = {
            student_id: studentId,
            exam_id: examId,
            answers: answers
        };

        try {
            const response = await fetch("/student/exam", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify(data)
            });
            const result = await response.json();
            if (result.status === "success") {
                // 清除倒计时
                if (countdownInterval) {
                    clearInterval(countdownInterval);
                    countdownInterval = null;
                }
                timerStatus.textContent = "已提交";
                timerStatus.className = "";
                
                alert(`答题提交成功！得分：${result.score}`);
                // 提交成功后跳转回学生界面
                window.location.href = "/student/";
            } else {
                alert(`提交失败：${result.message || "请重试"}`);
            }
        } catch (error) {
            console.error("Error:", error);
            alert("提交失败，请检查网络连接。");
        }
    });
});