document.addEventListener('DOMContentLoaded', function() {
    // 初始化成绩分布折线图
    if (document.getElementById('scoreDistributionChart')) {
        const scoreData = window.scoreDistributionData || [];
        const scoreRanges = ['0-9', '10-19', '20-29', '30-39', '40-49',
                            '50-59', '60-69', '70-79', '80-89', '90-100'];

        const ctx = document.getElementById('scoreDistributionChart').getContext('2d');
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: scoreRanges,
                datasets: [{
                    label: '人数分布',
                    data: scoreData,
                    borderColor: '#1e9fff',
                    backgroundColor: 'rgba(30, 159, 255, 0.1)',
                    tension: 0.4,
                    fill: true,
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: '成绩分布折线图'
                    },
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '人数'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: '分数区间'
                        }
                    }
                }
            }
        });
    }

    // 初始化学生成绩柱状图
    if (document.querySelector('.chart-container')) {
        const studentResults = window.studentResults || [];

        if (studentResults.length > 0) {
            const studentIds = studentResults.map(result => result.student_id || '未知');
            const studentScores = studentResults.map(result => result.score || 0);

            const chartContainer = document.querySelector('.chart-container');
            const canvas = document.createElement('canvas');
            canvas.id = 'studentScoresChart';
            chartContainer.innerHTML = '';
            chartContainer.appendChild(canvas);

            const ctx = canvas.getContext('2d');
            const chart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: studentIds,
                    datasets: [{
                        label: '学生成绩',
                        data: studentScores,
                        backgroundColor: 'rgba(54, 162, 235, 0.6)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: '成绩统计'
                        },
                        tooltip: {
                            callbacks: {
                                title: function(tooltipItems) {
                                    return '学号: ' + tooltipItems[0].label;
                                },
                                label: function(context) {
                                    return '成绩: ' + context.raw;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '成绩'
                            },
                            max: 100
                        },
                        x: {
                            title: {
                                display: true,
                                text: '学号'
                            },
                            ticks: {
                                maxRotation: 45,
                                minRotation: 45
                            }
                        }
                    }
                }
            });
        }
    }
});

// 导出成绩功能
function exportResults() {
    const studentResults = window.studentResults || [];
    if (studentResults.length === 0) {
        layer.msg('暂无成绩数据可导出', {icon: 2});
        return;
    }

    // 创建CSV内容
    let csvContent = "学号,姓名,得分,提交时间\n";
    studentResults.forEach(result => {
        csvContent += `${result.student_id || ''},${result.student_name || '未知'},${result.score || 0},${result.submitted_at || ''}\n`;
    });

    // 下载CSV文件
    downloadCSV(csvContent, '考试成绩.csv');
    layer.msg('成绩导出成功', {icon: 1});
}

// 导出错题功能
function exportWrongQuestions() {
    const questionStats = window.questionStats || [];
    const wrongQuestions = questionStats.filter(q => q.correct_rate < 60);

    if (wrongQuestions.length === 0) {
        layer.msg('暂无错题数据可导出', {icon: 2});
        return;
    }

    // 创建CSV内容
    let csvContent = "题目类型,题目内容,正确答案,正确率,错误率\n";
    wrongQuestions.forEach(question => {
        const errorRate = (100 - question.correct_rate).toFixed(2);
        csvContent += `${question.type_name || ''},${question.text || ''},${question.correct_answer || ''},${question.correct_rate.toFixed(2)}%,${errorRate}%\n`;
    });

    // 下载CSV文件
    downloadCSV(csvContent, '错题统计.csv');
    layer.msg('错题导出成功', {icon: 1});
}

// 查看学生详情
function viewStudentDetail(studentId) {
    const studentResults = window.studentResults || [];
    const student = studentResults.find(s => s.student_id === studentId);

    if (!student) {
        layer.msg('学生信息不存在', {icon: 2});
        return;
    }

    // 解析学生答题数据
    let answersData = {};
    try {
        answersData = JSON.parse(student.answers || '{}');
    } catch (e) {
        console.error('解析答题数据失败:', e);
    }

    // 构建详情内容
    let detailContent = `
        <div class="student-detail">
            <div class="student-info">
                <h3>学生信息</h3>
                <p><strong>学号:</strong> ${student.student_id}</p>
                <p><strong>姓名:</strong> ${student.student_name || '未知'}</p>
                <p><strong>得分:</strong> ${student.score}</p>
                <p><strong>提交时间:</strong> ${student.submitted_at || '未知'}</p>
            </div>
            <div class="answer-details">
                <h3>答题详情</h3>
                <div id="answerDetailsList"></div>
            </div>
        </div>
    `;

    layer.open({
        type: 1,
        title: '学生答题详情',
        area: ['800px', '600px'],
        content: detailContent,
        success: function(layero, index) {
            // 这里可以加载更详细的答题信息
            $('#answerDetailsList').html('<p style="color: #999;">答题详情加载中...</p>');

            // 模拟加载答题详情
            setTimeout(() => {
                let answersHtml = '';
                if (Object.keys(answersData).length > 0) {
                    Object.keys(answersData).forEach((questionId, idx) => {
                        const answer = answersData[questionId];
                        answersHtml += `
                            <div class="answer-item">
                                <p><strong>第${idx + 1}题:</strong> ${answer.question || '题目内容'}</p>
                                <p><strong>学生答案:</strong> ${answer.answer || '未作答'}</p>
                                <p><strong>正确答案:</strong> ${answer.correct_answer || '未知'}</p>
                                <p><strong>得分:</strong> ${answer.score || 0}</p>
                            </div>
                        `;
                    });
                } else {
                    answersHtml = '<p style="color: #999;">暂无详细答题数据</p>';
                }
                $('#answerDetailsList').html(answersHtml);
            }, 500);
        }
    });
}

// 搜索学生功能
function searchStudent() {
    const keyword = $('#studentSearchInput').val().trim().toLowerCase();
    const rows = $('#studentResultsTable tr');

    if (!keyword) {
        rows.show();
        return;
    }

    rows.each(function() {
        const row = $(this);
        const studentId = row.find('td:eq(1)').text().toLowerCase();
        const studentName = row.find('td:eq(2)').text().toLowerCase();

        if (studentId.includes(keyword) || studentName.includes(keyword)) {
            row.show();
        } else {
            row.hide();
        }
    });
}

// 下载CSV文件的辅助函数
function downloadCSV(csvContent, filename) {
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 绑定搜索框回车事件
$(document).ready(function() {
    $('#studentSearchInput').on('keypress', function(e) {
        if (e.which === 13) {
            searchStudent();
        }
    });
});