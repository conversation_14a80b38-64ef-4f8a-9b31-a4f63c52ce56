/* 基础样式 */
body {
    font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
    margin: 0;
    padding: 0;
}

/* 顶部导航栏样式 */
.top-navbar {
    background-color: #1a73e8;
    color: white;
    padding: 15px 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
}

.logo {
    position: absolute;
    left: 20px;
    font-weight: bold;
    font-size: 18px;
}

.nav-title {
    font-size: 24px;
    font-weight: 500;
}

.nav-actions {
    position: absolute;
    right: 20px;
}

.nav-links{
    list-style-type: none;
    margin: 0;
    padding: 0;
    overflow: hidden;
}

.nav-links li {
    float: left;
}

.nav-links a {
    display: block;
    color: white;
    text-align: center;
    padding: 14px 16px;
    text-decoration: none;
}

.container-1{
    padding: 120px;
    display: flex;
    justify-content: space-between;
    gap: 20px;
    flex-wrap: wrap;
    overflow: hidden;
}

.information {
    flex: 1;
    min-width: 250px;
    background: #fff;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.12);
    overflow: auto;
}

.course-schedule {
    flex: 2;
    min-width: 450px;
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.12);
    padding: 15px;
    overflow: auto;
}

.todo-list {
    flex: 1;
    min-width: 250px;
    background: #fff;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.12);
    overflow: auto;
}

.todo-list ul {
    padding-left: 20px;
    margin: 0;
}

.todo-list li {
    margin-bottom: 12px;
    line-height: 1.5;
    padding: 8px 0;
    list-style-type: disc;
}