/* 基础样式 */
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
}

/* 固定侧边栏样式 */
.sidebar-fixed {
    position: sticky;
    top: 20px;
    height: fit-content;
    max-height: calc(100vh - 40px);
    overflow-y: auto;
}

/* 中间内容区域固定样式 */
.content-fixed {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 40px);
}

.content-card {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.paper-info-header {
    flex-shrink: 0;
    border-bottom: 1px solid #f0f0f0;
    padding: 20px;
}

.content-scrollable {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
}

/* 当前课程信息样式 */
.current-course-info {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    padding: 12px 16px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #1E9FFF;
}

.course-label {
    font-weight: 500;
    color: #666;
    margin-right: 8px;
    white-space: nowrap;
}

.course-text {
    color: #333;
    font-weight: 500;
}

/* 试卷基本信息样式 */
.paper-basic-info {
    margin-top: 10px;
}

/* 题目类型按钮布局 */
.question-type-buttons {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.button-row {
    display: flex;
    gap: 8px;
}

.button-row.single-button {
    justify-content: center;
}

.question-type-btn {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 12px 8px;
    min-height: 60px;
    font-size: 12px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.question-type-btn:hover {
    background: #1E9FFF;
    color: white;
    border-color: #1E9FFF;
}

.question-type-btn i {
    font-size: 18px;
}

/* 考试设置样式 */
.paper-settings {
    padding: 20px;
}

.setting-item {
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.setting-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
    color: #333;
    margin-bottom: 10px;
}

.setting-label i {
    color: #1E9FFF;
}

.setting-actions .layui-btn {
    width: 100%; 
    box-sizing: border-box; 
    margin-left: 0;
    margin-right: 0;
}

.time-input-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.time-input-group input {
    flex: 1;
    text-align: center;
}

.time-unit {
    color: #666;
    font-size: 14px;
    white-space: nowrap;
}

.total-score-item {
    background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
    border-color: #1E9FFF;
}

.total-score-display {
    font-size: 24px;
    font-weight: bold;
    color: #1E9FFF;
    text-align: center;
    padding: 10px;
    background: white;
    border-radius: 6px;
    border: 2px solid #1E9FFF;
}

.setting-actions {
    margin-top: 20px;
}

.save-paper-btn {
    height: 45px;
    font-size: 16px;
    font-weight: 500;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.cancel-paper-btn {
    height: 45px;
    font-size: 16px;
    font-weight: 500;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-top: 10px;
}

/* 题目样式优化 */
.question {
    margin-bottom: 25px;
    padding: 20px;
    border: 1px solid #e6e6e6;
    border-radius: 8px;
    background: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.question:hover {
    border-color: #1E9FFF;
    box-shadow: 0 4px 12px rgba(30, 159, 255, 0.1);
}

/* 题目头部样式优化 */
.question-header {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
    gap: 12px;
}

.question-number {
    font-weight: bold;
    font-size: 18px;
    color: #1E9FFF;
    min-width: 30px;
    flex-shrink: 0;
    margin-top: 8px;
}

.question-header input {
    flex: 1;
    font-size: 16px;
    padding: 8px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    transition: border-color 0.3s ease;
}

.question-header input:focus {
    border-color: #1E9FFF;
    box-shadow: 0 0 0 2px rgba(30, 159, 255, 0.1);
}

/* 题目分值和删除按钮行样式 */
.question-score {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 12px 0;
}

.question-score-left {
    display: flex;
    align-items: center;
    gap: 8px;
}

.question-score label {
    font-weight: 500;
    color: #666;
    white-space: nowrap;
    font-size: 13px;
}

.question-score input {
    width: 60px;
    text-align: center;
    padding: 4px 6px;
    border: 1px solid #d9d9d9;
    border-radius: 3px;
    font-weight: 500;
    font-size: 13px;
}

.question-score input:focus {
    border-color: #1E9FFF;
    box-shadow: 0 0 0 1px rgba(30, 159, 255, 0.2);
}

/* 小尺寸删除题目按钮 */
.question-score .delete-btn {
    background-color: #ff4d4f;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 4px 12px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s ease;
}

.question-score .delete-btn:hover {
    background-color: #ff7875;
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(255, 77, 79, 0.3);
}

/* 选项容器样式优化 */
.options-container {
    margin: 15px 0;
    border: 1px solid #e6e6e6;
    border-radius: 8px;
    padding: 15px;
    background: #fafbfc;
}

/* 选项项样式优化 */
.option-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    padding: 12px 15px;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    background: #fff;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.option-item:last-of-type {
    margin-bottom: 0;
}

.option-item:hover {
    background: #f0f8ff;
    border-color: #1E9FFF;
    box-shadow: 0 2px 8px rgba(30, 159, 255, 0.1);
}

.option-item:hover .option-delete-btn {
    opacity: 1;
    visibility: visible;
}

.option-content {
    display: flex;
    align-items: center;
    flex: 1;
    gap: 12px;
}

/* 隐藏所有选框 */
.option-checkbox,
.options-container input[type="radio"],
.options-container input[type="checkbox"],
.option-item input[type="radio"],
.option-item input[type="checkbox"] {
    display: none !important;
}

/* 选项字母标识 */
.option-letter {
    font-weight: bold;
    color: #666;
    min-width: 24px;
    font-size: 16px;
    text-align: left;
}

/* 选项文本输入框 - 统一左对齐 */
.option-text {
    flex: 1;
    border: none;
    background: transparent;
    outline: none;
    padding: 6px 8px;
    border-radius: 4px;
    font-size: 14px;
    line-height: 1.5;
    text-align: left;
    width: auto !important;
    margin: 0;
}

.option-text:focus {
    background: white;
    border: 1px solid #1E9FFF;
    box-shadow: 0 0 0 2px rgba(30, 159, 255, 0.1);
}

/* 圆形删除按钮 */
.option-delete-btn {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: #ff4d4f;
    color: white;
    border: none;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    flex-shrink: 0;
    margin-left: 10px;
}

.option-delete-btn:hover {
    background-color: #ff7875;
    transform: scale(1.1);
}

.option-delete-btn::before {
    content: "−";
    font-weight: bold;
    font-size: 16px;
    line-height: 1;
}

/* 正确答案标识样式 */
.correct-answer {
    background: #52c41a;
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 12px;
    margin-left: 10px;
    white-space: nowrap;
}

/* 选中状态的选项样式 */
.option-item.correct {
    background: #f6ffed;
    border-color: #b7eb8f;
}

/* 选项输入框样式优化 */
.option-item .layui-input {
    border: none;
    background: transparent;
    padding: 4px 8px;
    margin: 0;
    height: auto;
    line-height: 1.5;
}

.option-item .layui-input:focus {
    background: white;
    border: 1px solid #1E9FFF;
    border-radius: 3px;
}

/* 添加选项按钮优化 */
.add-option {
    text-align: center;
    margin-top: 15px;
    padding-top: 15px;
}

.add-option-btn {
    background-color: #1890ff;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    cursor: pointer;
    font-size: 14px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    transition: all 0.3s ease;
}

.add-option-btn:hover {
    background-color: #40a9ff;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

/* 填空题和主观题答案样式 */
.fill-answer, .subjective-answer {
    margin-top: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.fill-answer label, .subjective-answer label {
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
    display: block;
}

.fill-answer input, .subjective-answer textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.fill-answer input:focus, .subjective-answer textarea:focus {
    border-color: #1E9FFF;
    box-shadow: 0 0 0 2px rgba(30, 159, 255, 0.1);
}

.fixed-option {
    font-weight: 500;
    color: #333;
}



/* 响应式设计 */
@media (max-width: 1200px) {
    .question-type-btn {
        font-size: 11px;
        padding: 10px 6px;
        min-height: 55px;
    }

    .question-type-btn i {
        font-size: 16px;
    }
}

@media (max-width: 768px) {
    .sidebar-fixed {
        position: static;
        height: auto;
        max-height: none;
    }

    .content-fixed {
        height: auto;
    }

    .button-row {
        flex-direction: column;
        gap: 8px;
    }

    .question-type-btn {
        flex-direction: row;
        justify-content: center;
        min-height: 40px;
        gap: 8px;
    }
}

/* 清除默认样式 */
* {
    box-sizing: border-box;
}

/* 滚动条样式 */
.content-scrollable::-webkit-scrollbar,
.sidebar-fixed::-webkit-scrollbar {
    width: 6px;
}

.content-scrollable::-webkit-scrollbar-track,
.sidebar-fixed::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.content-scrollable::-webkit-scrollbar-thumb,
.sidebar-fixed::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.content-scrollable::-webkit-scrollbar-thumb:hover,
.sidebar-fixed::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 导入习题按钮样式 */
.import-row .import-btn {
    background: #52c41a;
    border-color: #52c41a;
    color: white;
}

.import-row .import-btn:hover {
    background: #73d13d;
    border-color: #73d13d;
}

/* 导入习题弹窗样式 */
.import-exercise-container {
    max-height: 600px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.import-exercise-list {
    flex: 1;
    overflow-y: auto;
    margin: 10px 0;
    max-height: 400px;
}

.import-exercise-list .layui-table {
    margin: 0;
}

.import-exercise-list .layui-table th,
.import-exercise-list .layui-table td {
    padding: 8px 12px;
    font-size: 13px;
}

.import-exercise-list .layui-table tbody tr:hover {
    background-color: #f0f8ff;
}

/* 导入弹窗中的搜索筛选容器样式 */
.import-exercise-container .search-filter-container {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 15px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
    flex-shrink: 0;
}

.import-exercise-container .search-box {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
    max-width: 350px;
}

.import-exercise-container .search-input-wrapper {
    flex: 1;
}

.import-exercise-container .search-box .layui-input-group {
    border-radius: 6px;
    overflow: hidden;
    border: 1px solid #d9d9d9;
    background: #fff;
    display: flex;
    align-items: center;
}

.import-exercise-container .search-box .layui-input {
    border: none;
    padding: 6px 10px;
    font-size: 13px;
    flex: 1;
    outline: none;
}

.import-exercise-container .search-box .layui-input-prefix {
    padding: 0 10px;
    background: #fff;
    color: #999;
}

.import-exercise-container .search-box .layui-input-suffix {
    padding: 0 8px;
    background: #fff;
}

.import-exercise-container .clear-btn {
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    padding: 2px;
    border-radius: 2px;
}

.import-exercise-container .clear-btn:hover {
    color: #666;
    background: #f0f0f0;
}

.import-exercise-container .search-btn {
    padding: 6px 16px;
    background: #1E9FFF;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
}

.import-exercise-container .search-btn:hover {
    background: #40a9ff;
}

/* 筛选按钮样式 */
.import-exercise-container .filter-box {
    position: relative;
}

.import-exercise-container .filter-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    background: white;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    color: #666;
}

.import-exercise-container .filter-btn:hover {
    border-color: #1E9FFF;
    color: #1E9FFF;
}

.import-exercise-container .filter-panel {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    min-width: 250px;
    padding: 15px;
}

.import-exercise-container .filter-section {
    margin-bottom: 15px;
}

.import-exercise-container .filter-section:last-child {
    margin-bottom: 0;
}

.import-exercise-container .filter-label {
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
    font-size: 13px;
}

.import-exercise-container .filter-options {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.import-exercise-container .filter-option {
    display: flex;
    align-items: center;
    gap: 4px;
    cursor: pointer;
    font-size: 12px;
    color: #666;
}

.import-exercise-container .filter-option input[type="checkbox"] {
    margin: 0;
}

.import-exercise-container .filter-actions {
    display: flex;
    justify-content: space-between;
    gap: 10px;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #f0f0f0;
}

.import-exercise-container .clear-filter-btn,
.import-exercise-container .confirm-filter-btn {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
}

.import-exercise-container .clear-filter-btn {
    background: #f5f5f5;
    color: #666;
}

.import-exercise-container .clear-filter-btn:hover {
    background: #e6e6e6;
}

.import-exercise-container .confirm-filter-btn {
    background: #1E9FFF;
    color: white;
}

.import-exercise-container .confirm-filter-btn:hover {
    background: #40a9ff;
}

/* 题目统计样式 */
.import-exercise-container .question-count {
    font-size: 13px;
    color: #666;
    white-space: nowrap;
}

/* 导入操作按钮样式 */
.import-actions {
    flex-shrink: 0;
}

.import-actions .layui-btn {
    margin: 0 5px;
}

/* 表格复选框样式 */
.exercise-checkbox {
    transform: scale(1.1);
}