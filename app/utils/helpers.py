import os
import hashlib
from werkzeug.utils import secure_filename
from flask import current_app
from app.models.database import get_db
from datetime import datetime
import uuid


def allowed_file(filename):
    """检查文件类型是否允许"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in current_app.config['ALLOWED_EXTENSIONS']

def hash_password(password):
    """对密码进行MD5加密"""
    return hashlib.md5(password.encode()).hexdigest()

def verify_password(password, hashed_password):
    """验证密码"""
    return hash_password(password) == hashed_password

def save_uploaded_file(file, teacher_id, logical_folder_path='/'):
    """保存上传的文件

    Args:
        file: 上传的文件对象
        teacher_id: 教师ID
        logical_folder_path: 逻辑文件夹路径（如 /folder1/subfolder）

    Returns:
        tuple: (success, message, filename, physical_path)
    """
    try:
        if not file or file.filename == '':
            return False, "没有选择文件", None, None

        if not allowed_file(file.filename):
            allowed_exts = ', '.join(current_app.config['ALLOWED_EXTENSIONS'])
            return False, f"不支持的文件类型，支持的格式：{allowed_exts}", None, None

        # 检查文件大小
        file.seek(0, os.SEEK_END)
        file_length = file.tell()
        file.seek(0)
        max_size = current_app.config['MAX_CONTENT_LENGTH']
        if file_length > max_size:
            max_size_mb = max_size / (1024 * 1024)
            return False, f"文件太大，最大支持{max_size_mb:.0f}MB", None, None

        # 安全处理文件名
        filename = secure_filename(file.filename)
        if not filename:
            return False, "文件名无效", None, None

        # 构建物理存储路径
        # 基础路径：uploads/{teacher_id}
        base_upload_folder = os.path.join(current_app.config['UPLOAD_FOLDER'], str(teacher_id))

        # 将逻辑文件夹路径转换为物理路径
        # 去掉开头的 '/' 并替换为系统路径分隔符
        if logical_folder_path and logical_folder_path != '/':
            # 移除开头的 '/' 并分割路径
            folder_parts = logical_folder_path.strip('/').split('/')
            # 过滤空字符串并重新组合
            folder_parts = [part for part in folder_parts if part.strip()]
            if folder_parts:
                physical_folder_path = os.path.join(base_upload_folder, *folder_parts)
            else:
                physical_folder_path = base_upload_folder
        else:
            physical_folder_path = base_upload_folder

        # 确保目录存在
        try:
            if not os.path.exists(physical_folder_path):
                os.makedirs(physical_folder_path, mode=0o755)
        except OSError as e:
            return False, f"创建上传目录失败: {str(e)}", None, None

        # 检查文件是否已存在，如果存在则添加序号
        original_filename = filename
        counter = 1
        file_path = os.path.join(physical_folder_path, filename)

        while os.path.exists(file_path):
            name, ext = os.path.splitext(original_filename)
            filename = f"{name}_{counter}{ext}"
            file_path = os.path.join(physical_folder_path, filename)
            counter += 1

        # 保存文件
        try:
            file.save(file_path)

            # 验证文件是否成功保存
            if not os.path.exists(file_path):
                return False, "文件保存失败", None, None

            # 验证文件大小
            saved_size = os.path.getsize(file_path)
            if saved_size != file_length:
                os.remove(file_path)  # 删除不完整的文件
                return False, "文件保存不完整", None, None

        except Exception as e:
            return False, f"保存文件时出错: {str(e)}", None, None

        # 构建相对于uploads目录的路径用于数据库存储
        relative_path = os.path.relpath(file_path, current_app.config['UPLOAD_FOLDER'])
        # 统一使用正斜杠
        relative_path = relative_path.replace(os.sep, '/')

        return True, "文件上传成功", filename, relative_path

    except Exception as e:
        return False, f"上传过程中出错: {str(e)}", None, None

def get_file_size(file_path):
    """获取文件大小"""
    try:
        return os.path.getsize(file_path)
    except OSError:
        return 0

def get_file_type(filename):
    """根据文件名获取文件类型"""
    if '.' not in filename:
        return 'unknown'

    ext = filename.rsplit('.', 1)[1].lower()

    # 文档类型
    if ext in ['doc', 'docx']:
        return 'word'
    elif ext in ['xls', 'xlsx']:
        return 'excel'
    elif ext in ['ppt', 'pptx']:
        return 'powerpoint'
    elif ext == 'pdf':
        return 'pdf'
    elif ext == 'txt':
        return 'text'

    # 图片类型
    elif ext in ['jpg', 'jpeg', 'png', 'gif', 'bmp']:
        return 'image'

    # 压缩文件
    elif ext in ['zip', 'rar', '7z']:
        return 'archive'

    # 音视频
    elif ext in ['mp3', 'wav', 'mp4', 'avi', 'mov']:
        return 'media'

    else:
        return 'file'

def get_physical_folder_path(teacher_id, logical_folder_path):
    """将逻辑文件夹路径转换为物理路径

    Args:
        teacher_id: 教师ID
        logical_folder_path: 逻辑文件夹路径（如 /folder1/subfolder）

    Returns:
        str: 物理文件夹路径
    """
    base_upload_folder = os.path.join(current_app.config['UPLOAD_FOLDER'], teacher_id)

    if logical_folder_path and logical_folder_path != '/':
        # 移除开头的 '/' 并分割路径
        folder_parts = logical_folder_path.strip('/').split('/')
        # 过滤空字符串并重新组合
        folder_parts = [part for part in folder_parts if part.strip()]
        if folder_parts:
            return os.path.join(base_upload_folder, *folder_parts)

    return base_upload_folder

def move_physical_file(teacher_id, old_file_path, new_logical_folder_path):
    """移动物理文件到新的文件夹

    Args:
        teacher_id: 教师ID
        old_file_path: 旧的文件路径（相对于uploads目录）
        new_logical_folder_path: 新的逻辑文件夹路径

    Returns:
        tuple: (success, message, new_file_path)
    """
    try:
        # 构建旧文件的完整路径
        old_full_path = os.path.join(current_app.config['UPLOAD_FOLDER'], old_file_path)

        if not os.path.exists(old_full_path):
            return False, "源文件不存在", None

        # 获取文件名
        filename = os.path.basename(old_full_path)

        # 构建新的物理文件夹路径
        new_physical_folder = get_physical_folder_path(teacher_id, new_logical_folder_path)

        # 确保新目录存在
        if not os.path.exists(new_physical_folder):
            os.makedirs(new_physical_folder, mode=0o755)

        # 构建新文件路径
        new_full_path = os.path.join(new_physical_folder, filename)

        # 如果目标文件已存在，添加序号
        original_filename = filename
        counter = 1
        while os.path.exists(new_full_path):
            name, ext = os.path.splitext(original_filename)
            filename = f"{name}_{counter}{ext}"
            new_full_path = os.path.join(new_physical_folder, filename)
            counter += 1

        # 移动文件
        import shutil
        shutil.move(old_full_path, new_full_path)

        # 构建新的相对路径
        new_relative_path = os.path.relpath(new_full_path, current_app.config['UPLOAD_FOLDER'])
        new_relative_path = new_relative_path.replace(os.sep, '/')

        return True, "文件移动成功", new_relative_path

    except Exception as e:
        return False, f"移动文件时出错: {str(e)}", None

def create_physical_folder(teacher_id, logical_folder_path):
    """创建物理文件夹和数据库中的文件夹记录

    Args:
        teacher_id: 教师ID
        logical_folder_path: 逻辑文件夹路径

    Returns:
        tuple: (success, message)
    """
    try:
        # 创建物理文件夹
        physical_path = get_physical_folder_path(teacher_id, logical_folder_path)

        if not os.path.exists(physical_path):
            os.makedirs(physical_path, mode=0o755)

        # 在数据库中创建文件夹记录
        conn = get_db()
        cursor = conn.cursor()

        # 获取文件夹名称和父路径
        folder_name = os.path.basename(logical_folder_path.rstrip('/'))
        if not folder_name:  # 如果是根目录
            folder_name = '/'
            parent_path = ''
        else:
            parent_path = os.path.dirname(logical_folder_path)
            if not parent_path or parent_path == '/':
                parent_path = '/'

        # 检查文件夹是否已存在
        cursor.execute("""
            SELECT id FROM teacher_folders 
            WHERE teacher_id = ? AND folder_path = ?
        """, (teacher_id, logical_folder_path))

        if cursor.fetchone():
            conn.close()
            return True, "文件夹已存在"

        # 检查父文件夹是否存在（根目录除外）
        if parent_path != '/' and parent_path != '':
            cursor.execute("""
                SELECT id FROM teacher_folders
                WHERE teacher_id = ? AND folder_path = ?
            """, (teacher_id, parent_path))

            if not cursor.fetchone():
                conn.close()
                return False, "父文件夹不存在"

        # 创建文件夹记录
        folder_id = str(uuid.uuid4())
        current_time = datetime.now().isoformat()

        cursor.execute("""
            INSERT INTO teacher_folders (id, teacher_id, folder_path, folder_name, parent_path, created_at)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (folder_id, teacher_id, logical_folder_path, folder_name, parent_path, current_time))

        conn.commit()
        conn.close()

        return True, "文件夹创建成功"

    except Exception as e:
        return False, f"创建文件夹时出错: {str(e)}"

def delete_physical_folder(teacher_id, logical_folder_path):
    """删除物理文件夹和数据库中的文件夹记录（仅当为空时）

    Args:
        teacher_id: 教师ID
        logical_folder_path: 逻辑文件夹路径

    Returns:
        tuple: (success, message)
    """
    try:

        # 获取物理文件夹路径
        physical_path = get_physical_folder_path(str(teacher_id), logical_folder_path)

        if not os.path.exists(physical_path):
            # 删除数据库记录（如果存在）
            conn = get_db()
            cursor = conn.cursor()
            cursor.execute("""
                DELETE FROM teacher_folders
                WHERE teacher_id = ? AND folder_path = ?
            """, (teacher_id, logical_folder_path))
            conn.commit()
            conn.close()
            return True, "文件夹不存在或已被删除"

        # 检查文件夹是否为空
        try:
            contents = os.listdir(physical_path)
            if contents:
                # 详细列出文件夹内容供调试
                files = [f for f in contents if os.path.isfile(os.path.join(physical_path, f))]
                folders = [d for d in contents if os.path.isdir(os.path.join(physical_path, d))]
                
                message = "文件夹不为空。"
                if files:
                    message += f"包含文件: {', '.join(files[:3])}"
                    if len(files) > 3:
                        message += f" 等{len(files)}个文件"
                if folders:
                    if files:
                        message += "；"
                    message += f"包含子文件夹: {', '.join(folders[:3])}"
                    if len(folders) > 3:
                        message += f" 等{len(folders)}个文件夹"
                return False, message
        except OSError as e:
            return False, f"检查文件夹内容时出错: {str(e)}"

        try:
            # 删除物理文件夹
            os.rmdir(physical_path)

            # 删除数据库记录
            conn = get_db()
            cursor = conn.cursor()

            # 检查是否存在子文件夹
            cursor.execute("""
                SELECT EXISTS(
                    SELECT 1 FROM teacher_folders
                    WHERE teacher_id = ? AND parent_path = ?
                ) as has_subfolders
            """, (teacher_id, logical_folder_path))

            result = cursor.fetchone()
            if result['has_subfolders']:
                return False, "文件夹包含子文件夹，无法删除"

            # 删除文件夹记录
            cursor.execute("""
                DELETE FROM teacher_folders
                WHERE teacher_id = ? AND folder_path = ?
            """, (teacher_id, logical_folder_path))

            conn.commit()
            conn.close()

            return True, "文件夹删除成功"
        except OSError as e:
            return False, f"删除文件夹时出错: {str(e)}"

    except Exception as e:
        return False, f"删除文件夹时出错: {str(e)}"
