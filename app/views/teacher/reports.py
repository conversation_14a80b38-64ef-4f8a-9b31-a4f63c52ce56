"""
课堂报告相关路由
"""
from flask import render_template, request, jsonify, session, make_response
from app.models.database import get_db
from app.utils.decorators import teacher_required
from datetime import datetime
import json
import csv
import io
import uuid
import json
import csv
import io


def register_routes(bp):
    """注册课堂报告相关路由"""

    @bp.route('/reports')
    @teacher_required
    def reports():
        """课堂报告页面 - 实时显示"""
        # 获取当前选中的课堂
        current_class_id = session.get('current_class_id')
        if not current_class_id:
            return render_template("teacher/reports.html", current_page="reports", current_class=None)

        conn = get_db()
        cursor = conn.cursor()

        # 获取当前课堂信息
        cursor.execute("""
            SELECT cs.id, c.name as course_name, c.code as course_code,
                   cl.name as classroom_name, cls.name as class_name,
                   cs.day_of_week, cs.start_time, cs.end_time, cs.status,
                   cs.start_datetime, cs.description
            FROM course_schedules cs
            JOIN courses c ON cs.course_id = c.id
            JOIN classrooms cl ON cs.classroom_id = cl.id
            JOIN classes cls ON cs.class_id = cls.id
            WHERE cs.id = ? AND cs.teacher_id = ?
        """, (current_class_id, session.get('teacher_id')))

        current_class = cursor.fetchone()
        conn.close()

        if not current_class:
            return render_template("teacher/reports.html", current_page="reports", current_class=None)

        return render_template("teacher/reports.html", current_page="reports", current_class=current_class)

    @bp.route('/get_realtime_data/<course_schedule_id>', methods=['GET'])
    @teacher_required
    def get_realtime_data(course_schedule_id):
        """获取课堂实时数据"""
        try:
            conn = get_db()
            cursor = conn.cursor()

            # 验证课程权限
            cursor.execute("""
                SELECT cs.id, c.name as course_name, c.code as course_code,
                       cl.name as classroom_name, cls.name as class_name,
                       cs.day_of_week, cs.start_time, cs.end_time
                FROM course_schedules cs
                JOIN courses c ON cs.course_id = c.id
                JOIN classrooms cl ON cs.classroom_id = cl.id
                JOIN classes cls ON cs.class_id = cls.id
                WHERE cs.id = ? AND cs.teacher_id = ?
            """, (course_schedule_id, session.get('teacher_id')))

            course_info = cursor.fetchone()
            if not course_info:
                conn.close()
                return jsonify({"status": "error", "message": "无权限访问该课程"}), 403

            # 获取签到统计
            cursor.execute("""
                SELECT COUNT(*) as attendance_count
                FROM class_attendance
                WHERE course_schedule_id = ? AND status = 'present'
            """, (course_schedule_id,))
            attendance_result = cursor.fetchone()
            attendance_count = attendance_result['attendance_count'] if attendance_result else 0

            # 获取班级总人数
            cursor.execute("""
                SELECT COUNT(*) as total_students
                FROM students s
                JOIN course_schedules cs ON cs.class_id = s.class_id
                WHERE cs.id = ?
            """, (course_schedule_id,))
            total_result = cursor.fetchone()
            total_students = total_result['total_students'] if total_result else 0

            # 获取弹幕统计
            cursor.execute("""
                SELECT COUNT(*) as danmaku_count
                FROM danmaku
                WHERE course_schedule_id = ?
            """, (course_schedule_id,))
            danmaku_result = cursor.fetchone()
            danmaku_count = danmaku_result['danmaku_count'] if danmaku_result else 0

            # 获取发送弹幕的学生数
            cursor.execute("""
                SELECT COUNT(DISTINCT student_id) as active_students
                FROM danmaku
                WHERE course_schedule_id = ?
            """, (course_schedule_id,))
            active_result = cursor.fetchone()
            active_students = active_result['active_students'] if active_result else 0

            # 获取作业统计
            cursor.execute("""
                SELECT COUNT(*) as homework_count
                FROM homework
                WHERE course_schedule_id = ?
            """, (course_schedule_id,))
            homework_result = cursor.fetchone()
            homework_count = homework_result['homework_count'] if homework_result else 0

            # 获取考勤详情
            cursor.execute("""
                SELECT s.student_id, s.name, a.status, a.signin_time
                FROM students s
                LEFT JOIN class_attendance a ON a.student_id = s.student_id AND a.course_schedule_id = ?
                JOIN course_schedules cs ON cs.class_id = s.class_id
                WHERE cs.id = ?
                ORDER BY s.name
            """, (course_schedule_id, course_schedule_id))
            attendance_details = cursor.fetchall()

            # 获取弹幕详情
            cursor.execute("""
                SELECT d.student_id, s.name as student_name, COUNT(*) as danmaku_count
                FROM danmaku d
                JOIN students s ON d.student_id = s.student_id
                WHERE d.course_schedule_id = ?
                GROUP BY d.student_id, s.name
                ORDER BY danmaku_count DESC
            """, (course_schedule_id,))
            danmaku_details = cursor.fetchall()

            # 获取作业详情
            cursor.execute("""
                SELECT h.id, h.title, h.created_at, h.data
                FROM homework h
                WHERE h.course_schedule_id = ?
                ORDER BY h.created_at DESC
            """, (course_schedule_id,))
            homework_raw_data = cursor.fetchall()

            # 处理作业数据，计算题目数量和提交情况
            homework_details = []
            for homework in homework_raw_data:
                # 解析作业数据获取题目数量
                homework_data = json.loads(homework['data']) if homework['data'] else {}
                question_count = len(homework_data.get('questions', []))

                # 获取提交统计
                cursor.execute("""
                    SELECT COUNT(DISTINCT student_id) as submitted_count
                    FROM homework_results
                    WHERE homework_id = ?
                """, (homework['id'],))
                submit_result = cursor.fetchone()
                submitted_count = submit_result['submitted_count'] if submit_result else 0

                homework_details.append({
                    'id': homework['id'],
                    'title': homework['title'],
                    'created_at': homework['created_at'],
                    'question_count': question_count,
                    'submitted_count': submitted_count,
                    'total_students': total_students
                })

            conn.close()

            # 返回实时数据
            response_data = {
                "course_name": course_info['course_name'],
                "classroom_name": course_info['classroom_name'],
                "class_name": course_info['class_name'],
                "report_date": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "attendance_count": attendance_count,
                "total_students": total_students,
                "danmaku_count": danmaku_count,
                "active_students": active_students,
                "homework_count": homework_count,
                "attendance_details": [dict(row) for row in attendance_details],
                "danmaku_details": [dict(row) for row in danmaku_details],
                "homework_details": homework_details
            }

            return jsonify({"status": "success", "data": response_data})

        except Exception as e:
            return jsonify({"status": "error", "message": f"获取实时数据失败: {str(e)}"}), 500

    @bp.route('/export_report/<course_schedule_id>', methods=['GET'])
    @teacher_required
    def export_report(course_schedule_id):
        """导出课堂报告"""
        try:
            conn = get_db()
            cursor = conn.cursor()

            # 验证课程权限
            cursor.execute("""
                SELECT cs.id, c.name as course_name, c.code as course_code,
                       cl.name as classroom_name, cls.name as class_name,
                       cs.day_of_week, cs.start_time, cs.end_time
                FROM course_schedules cs
                JOIN courses c ON cs.course_id = c.id
                JOIN classrooms cl ON cs.classroom_id = cl.id
                JOIN classes cls ON cs.class_id = cls.id
                WHERE cs.id = ? AND cs.teacher_id = ?
            """, (course_schedule_id, session.get('teacher_id')))

            course_info = cursor.fetchone()
            if not course_info:
                conn.close()
                return jsonify({"status": "error", "message": "无权限访问该课程"}), 403

            # 获取签到统计
            cursor.execute("""
                SELECT COUNT(*) as attendance_count
                FROM class_attendance
                WHERE course_schedule_id = ? AND status = 'present'
            """, (course_schedule_id,))
            attendance_result = cursor.fetchone()
            attendance_count = attendance_result['attendance_count'] if attendance_result else 0

            # 获取班级总人数
            cursor.execute("""
                SELECT COUNT(*) as total_students
                FROM students s
                JOIN course_schedules cs ON cs.class_id = s.class_id
                WHERE cs.id = ?
            """, (course_schedule_id,))
            total_result = cursor.fetchone()
            total_students = total_result['total_students'] if total_result else 0

            # 获取弹幕统计
            cursor.execute("""
                SELECT COUNT(*) as danmaku_count
                FROM danmaku
                WHERE course_schedule_id = ?
            """, (course_schedule_id,))
            danmaku_result = cursor.fetchone()
            danmaku_count = danmaku_result['danmaku_count'] if danmaku_result else 0

            # 获取发送弹幕的学生数
            cursor.execute("""
                SELECT COUNT(DISTINCT student_id) as active_students
                FROM danmaku
                WHERE course_schedule_id = ?
            """, (course_schedule_id,))
            active_result = cursor.fetchone()
            active_students = active_result['active_students'] if active_result else 0

            # 获取作业统计
            cursor.execute("""
                SELECT COUNT(*) as homework_count
                FROM homework
                WHERE course_schedule_id = ?
            """, (course_schedule_id,))
            homework_result = cursor.fetchone()
            homework_count = homework_result['homework_count'] if homework_result else 0

            # 获取考勤详情
            cursor.execute("""
                SELECT s.student_id, s.name, a.status, a.signin_time
                FROM students s
                LEFT JOIN class_attendance a ON a.student_id = s.student_id AND a.course_schedule_id = ?
                JOIN course_schedules cs ON cs.class_id = s.class_id
                WHERE cs.id = ?
                ORDER BY s.name
            """, (course_schedule_id, course_schedule_id))
            attendance_details = cursor.fetchall()

            # 获取弹幕详情
            cursor.execute("""
                SELECT d.student_id, s.name as student_name, COUNT(*) as danmaku_count
                FROM danmaku d
                JOIN students s ON d.student_id = s.student_id
                WHERE d.course_schedule_id = ?
                GROUP BY d.student_id, s.name
                ORDER BY danmaku_count DESC
            """, (course_schedule_id,))
            danmaku_details = cursor.fetchall()

            # 获取作业详情
            cursor.execute("""
                SELECT h.id, h.title, h.created_at, h.data
                FROM homework h
                WHERE h.course_schedule_id = ?
                ORDER BY h.created_at DESC
            """, (course_schedule_id,))
            homework_raw_data = cursor.fetchall()

            conn.close()

            # 生成CSV内容
            output = io.StringIO()
            writer = csv.writer(output)

            # 写入课程信息
            writer.writerow(['课堂报告'])
            writer.writerow(['课程名称', course_info['course_name']])
            writer.writerow(['课程代码', course_info['course_code']])
            writer.writerow(['教室', course_info['classroom_name']])
            writer.writerow(['班级', course_info['class_name']])
            writer.writerow(['时间', f"{course_info['day_of_week']} {course_info['start_time']}-{course_info['end_time']}"])
            writer.writerow(['报告生成时间', datetime.now().strftime('%Y-%m-%d %H:%M:%S')])
            writer.writerow([])

            # 写入统计概览
            writer.writerow(['统计概览'])
            writer.writerow(['签到人数', attendance_count])
            writer.writerow(['班级总人数', total_students])
            attendance_rate = round((attendance_count / total_students) * 100, 1) if total_students > 0 else 0
            writer.writerow(['出勤率', f"{attendance_rate}%"])
            writer.writerow(['弹幕总数', danmaku_count])
            writer.writerow(['互动学生数', active_students])
            interaction_rate = round((active_students / total_students) * 100, 1) if total_students > 0 else 0
            writer.writerow(['互动率', f"{interaction_rate}%"])
            writer.writerow(['作业数量', homework_count])
            writer.writerow([])

            # 写入考勤详情
            writer.writerow(['考勤详情'])
            writer.writerow(['学号', '姓名', '签到状态', '签到时间'])
            for student in attendance_details:
                status_text = '已签到' if student['status'] == 'present' else '未签到'
                signin_time = student['signin_time'][:19] if student['signin_time'] else ''
                writer.writerow([student['student_id'], student['name'], status_text, signin_time])
            writer.writerow([])

            # 写入弹幕互动详情
            writer.writerow(['弹幕互动详情'])
            writer.writerow(['学号', '姓名', '发送弹幕数'])
            for student in danmaku_details:
                writer.writerow([student['student_id'], student['student_name'], student['danmaku_count']])
            writer.writerow([])

            # 写入作业详情
            if homework_raw_data:
                writer.writerow(['作业详情'])
                writer.writerow(['作业标题', '创建时间', '题目数量'])
                for homework in homework_raw_data:
                    homework_data = json.loads(homework['data']) if homework['data'] else {}
                    question_count = len(homework_data.get('questions', []))
                    writer.writerow([homework['title'], homework['created_at'][:19], question_count])

            # 创建响应
            response = make_response(output.getvalue())
            response.headers['Content-Type'] = 'text/csv; charset=utf-8-sig'
            filename = f"class_report_{course_info['course_code']}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            response.headers['Content-Disposition'] = f'attachment; filename={filename}'

            return response

        except Exception as e:
            return jsonify({"status": "error", "message": f"导出失败: {str(e)}"}), 500
