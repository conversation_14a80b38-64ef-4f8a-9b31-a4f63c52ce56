"""
教师认证相关路由
"""
from flask import render_template, request, jsonify, redirect, url_for, session
from app.models.database import get_db
from app.utils.decorators import teacher_required


def register_routes(bp):
    """注册认证相关路由"""
    
    @bp.route('/login')
    def login():
        """教师登录页面（重定向到统一登录）"""
        return redirect(url_for('auth.login'))

    @bp.route('/logout')
    def logout():
        """教师登出"""
        # 只清除教师相关的会话数据，保留学生的会话数据
        for key in list(session.keys()):
            if key.startswith('teacher_'):
                session.pop(key, None)
        return redirect(url_for('auth.login'))

    @bp.route('/')
    @teacher_required
    def index():
        conn = get_db()
        cursor = conn.cursor()

        # 获取当前教师的所有课程安排
        cursor.execute("""
            SELECT cs.id, c.name as course_name, c.code as course_code,
                   cl.name as classroom_name, cls.name as class_name,
                   cs.day_of_week, cs.start_time, cs.end_time, cs.status,
                   cs.start_datetime, cs.description
            FROM course_schedules cs
            JOIN courses c ON cs.course_id = c.id
            JOIN classrooms cl ON cs.classroom_id = cl.id
            JOIN classes cls ON cs.class_id = cls.id
            WHERE cs.teacher_id = ?
            ORDER BY
                CASE cs.day_of_week
                    WHEN '星期一' THEN 1
                    WHEN '星期二' THEN 2
                    WHEN '星期三' THEN 3
                    WHEN '星期四' THEN 4
                    WHEN '星期五' THEN 5
                    WHEN '星期六' THEN 6
                    WHEN '星期日' THEN 7
                END,
                cs.start_time
        """, (session.get('teacher_id'),))

        course_schedules = cursor.fetchall()

        # 获取当前选中的课堂（从session中获取）
        current_class_id = session.get('current_class_id')
        current_class = None

        if current_class_id:
            # 验证当前选中的课堂是否属于当前教师
            cursor.execute("""
                SELECT cs.id, c.name as course_name, c.code as course_code,
                       cl.name as classroom_name, cls.name as class_name,
                       cs.day_of_week, cs.start_time, cs.end_time, cs.status,
                       cs.start_datetime, cs.description
                FROM course_schedules cs
                JOIN courses c ON cs.course_id = c.id
                JOIN classrooms cl ON cs.classroom_id = cl.id
                JOIN classes cls ON cs.class_id = cls.id
                WHERE cs.id = ? AND cs.teacher_id = ?
            """, (current_class_id, session.get('teacher_id')))

            current_class = cursor.fetchone()

            # 如果课堂不存在或不属于当前教师，清除session
            if not current_class:
                session.pop('current_class_id', None)

        conn.close()

        return render_template("teacher/teacher.html",
                              current_page="index",
                              course_schedules=course_schedules,
                              current_class=current_class)


