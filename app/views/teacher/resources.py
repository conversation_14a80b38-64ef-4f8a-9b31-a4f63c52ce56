"""
资源库管理相关路由
"""
from flask import render_template, request, jsonify, session, current_app
from app.models.database import get_db
from app.utils.decorators import teacher_required
from app.utils.helpers import save_uploaded_file, get_file_size, get_file_type, move_physical_file, create_physical_folder, delete_physical_folder
from datetime import datetime
import uuid
import os


def register_routes(bp):
    """注册资源库管理相关路由"""
    
    @bp.route('/resources')
    @teacher_required
    def resources():
        """资源库主页"""
        return render_template("teacher/resources.html", current_page="resources")

    @bp.route('/resources/materials')
    @teacher_required
    def materials():
        """课件资源管理页面"""
        try:
            conn = get_db()
            cursor = conn.cursor()

            # 获取当前教师的所有课件
            cursor.execute("""
                SELECT id, title, file_path, file_type, file_size, upload_time, folder_path, description
                FROM teacher_materials
                WHERE teacher_id = ? AND course_schedule_id IS NULL
                ORDER BY upload_time DESC
            """, (session.get('teacher_id'),))

            materials = cursor.fetchall()
            conn.close()

            # 按文件夹路径组织数据
            folders = {}

            # 获取所有唯一的文件夹路径
            folder_paths = set()
            for material in materials:
                folder_path = material['folder_path'] or '/'
                folder_paths.add(folder_path)

            # 确保根目录存在
            folder_paths.add('/')

            # 为每个文件夹路径初始化空列表
            for folder_path in folder_paths:
                folders[folder_path] = []

            # 将文件分配到对应的文件夹
            for material in materials:
                folder_path = material['folder_path'] or '/'
                folders[folder_path].append({
                    'id': material['id'],
                    'title': material['title'],
                    'file_path': material['file_path'],
                    'file_type': material['file_type'],
                    'file_size': material['file_size'],
                    'upload_time': material['upload_time'],
                    'description': material['description']
                })

            return render_template("teacher/materials.html",
                                 current_page="resources",
                                 folders=folders)

        except Exception as e:
            # 如果出错，至少传递一个空的folders字典
            return render_template("teacher/materials.html",
                                 current_page="resources",
                                 folders={'/': []})

    @bp.route('/get_materials', methods=['GET'])
    @teacher_required
    def get_materials():
        """获取课件列表"""
        try:
            conn = get_db()
            cursor = conn.cursor()

            # 获取筛选参数
            keyword = request.args.get('keyword', '').strip()
            folder_path = request.args.get('folder_path', '/').strip()

            # 构建查询
            base_query = """
                SELECT id, title, file_path, file_type, file_size, upload_time, folder_path, description
                FROM teacher_materials
                WHERE teacher_id = ? AND course_schedule_id IS NULL
            """
            params = [session.get('teacher_id')]

            # 添加文件夹筛选
            if folder_path != '/':
                base_query += " AND folder_path = ?"
                params.append(folder_path)
            else:
                base_query += " AND folder_path = '/'"

            # 添加关键词搜索
            if keyword:
                base_query += " AND (title LIKE ? OR description LIKE ?)"
                params.extend([f'%{keyword}%', f'%{keyword}%'])

            base_query += " ORDER BY upload_time DESC"

            cursor.execute(base_query, params)
            materials = cursor.fetchall()

            # 转换为字典格式
            materials_list = []
            for material in materials:
                materials_list.append({
                    'id': material['id'],
                    'title': material['title'],
                    'file_path': material['file_path'],
                    'file_type': material['file_type'],
                    'file_size': material['file_size'],
                    'upload_time': material['upload_time'],
                    'folder_path': material['folder_path'],
                    'description': material['description']
                })

            conn.close()
            return jsonify({"status": "success", "materials": materials_list})

        except Exception as e:
            return jsonify({"status": "error", "message": f"获取课件失败: {str(e)}"}), 500

    @bp.route('/upload_materials', methods=['POST'])
    @teacher_required
    def upload_materials():
        """上传课件到资源库"""
        conn = None
        try:
            # 检查是否有文件被上传
            if 'files' not in request.files:
                return jsonify({"status": "error", "message": "没有选择文件"}), 400

            files = request.files.getlist('files')
            if not files or all(file.filename == '' for file in files):
                return jsonify({"status": "error", "message": "没有选择文件"}), 400

            # 获取表单数据
            folder_path = request.form.get('folder_path', '/').strip()
            description = request.form.get('description', '').strip()

            teacher_id = session.get('teacher_id')
            uploaded_files = []
            failed_files = []

            conn = get_db()
            cursor = conn.cursor()

            # 开始事务
            conn.execute('BEGIN TRANSACTION')

            for file in files:
                if file.filename == '':
                    continue

                try:
                    # 使用工具函数保存文件
                    success, message, filename, relative_path = save_uploaded_file(file, teacher_id, folder_path)

                    if not success:
                        failed_files.append(f"{file.filename}: {message}")
                        continue

                    # 获取文件信息
                    full_file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], relative_path)
                    file_size = get_file_size(full_file_path)
                    file_type = get_file_type(filename)

                    # 生成课件ID
                    material_id = str(uuid.uuid4())
                    current_time = datetime.now().isoformat()

                    # 插入课件记录（不关联课程）
                    cursor.execute("""
                        INSERT INTO teacher_materials (id, teacher_id, title, file_path, file_type, file_size, upload_time, folder_path, description, course_schedule_id)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        material_id,
                        teacher_id,
                        filename,
                        f"/uploads/{relative_path}",
                        file_type,
                        file_size,
                        current_time,
                        folder_path,
                        description,
                        None  # 资源库中的课件不关联课程
                    ))

                    uploaded_files.append(filename)

                except Exception as file_error:
                    failed_files.append(f"{file.filename}: {str(file_error)}")
                    continue

            # 提交事务
            conn.commit()
            conn.close()

            if uploaded_files:
                message = f"成功上传 {len(uploaded_files)} 个课件"
                if failed_files:
                    message += f"，{len(failed_files)} 个文件失败"

                return jsonify({
                    "status": "success",
                    "message": message,
                    "files": uploaded_files,
                    "failed_files": failed_files if failed_files else []
                })
            else:
                error_message = "所有文件上传失败"
                if failed_files:
                    error_message += f": {'; '.join(failed_files[:3])}"
                return jsonify({"status": "error", "message": error_message}), 400

        except Exception as e:
            if conn:
                conn.close()
            return jsonify({"status": "error", "message": f"上传失败: {str(e)}"}), 500

    @bp.route('/delete_material/<material_id>', methods=['DELETE'])
    @teacher_required
    def delete_material(material_id):
        """删除课件"""
        try:
            teacher_id = session.get('teacher_id')
            conn = get_db()
            cursor = conn.cursor()

            # 验证课件是否属于当前教师且在资源库中
            cursor.execute("""
                SELECT file_path FROM teacher_materials
                WHERE id = ? AND teacher_id = ? AND course_schedule_id IS NULL
            """, (material_id, teacher_id))

            material = cursor.fetchone()
            if not material:
                conn.close()
                return jsonify({"status": "error", "message": "课件不存在或无权限"}), 404

            # 删除数据库记录
            cursor.execute("DELETE FROM teacher_materials WHERE id = ?", (material_id,))

            # 删除物理文件
            try:
                file_path = material['file_path']
                if file_path.startswith('/uploads/'):
                    # 构建完整的文件路径
                    relative_path = file_path[9:]  # 去掉 '/uploads/' 前缀
                    full_path = os.path.join(current_app.config['UPLOAD_FOLDER'], relative_path)
                    if os.path.exists(full_path):
                        os.remove(full_path)
                        current_app.logger.info(f"成功删除物理文件: {full_path}")
                    else:
                        current_app.logger.warning(f"物理文件不存在: {full_path}")
            except Exception as e:
                current_app.logger.error(f"删除文件时出错: {e}")

            conn.commit()
            conn.close()

            return jsonify({"status": "success", "message": "课件删除成功"})

        except Exception as e:
            return jsonify({"status": "error", "message": f"删除失败: {str(e)}"}), 500

    @bp.route('/api/material_count')
    @teacher_required
    def api_material_count():
        """获取课件数量"""
        conn = get_db()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT COUNT(*) as count
            FROM teacher_materials
            WHERE teacher_id = ? AND course_schedule_id IS NULL
        """, (session.get('teacher_id'),))

        result = cursor.fetchone()
        count = result['count'] if result else 0

        conn.close()
        return jsonify({"status": "success", "count": count})

    @bp.route('/create_folder', methods=['POST'])
    @teacher_required
    def create_folder():
        """创建文件夹"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({"status": "error", "message": "无效的请求数据"}), 400

            folder_name = data.get('folder_name', '').strip()
            parent_path = data.get('parent_path', '/').strip()

            if not folder_name:
                return jsonify({"status": "error", "message": "文件夹名称不能为空"}), 400

            # 构建新文件夹路径
            if parent_path == '/':
                new_folder_path = f"/{folder_name}"
            else:
                new_folder_path = f"{parent_path}/{folder_name}"

            # 创建物理文件夹
            teacher_id = session.get('teacher_id')
            success, message = create_physical_folder(teacher_id, new_folder_path)

            if not success:
                return jsonify({"status": "error", "message": message}), 400

            return jsonify({"status": "success", "message": "文件夹创建成功", "folder_path": new_folder_path})

        except Exception as e:
            return jsonify({"status": "error", "message": f"创建文件夹失败: {str(e)}"}), 500

    @bp.route('/move_material', methods=['POST'])
    @teacher_required
    def move_material():
        """移动课件到指定文件夹"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({"status": "error", "message": "无效的请求数据"}), 400

            material_id = data.get('material_id')
            target_folder = data.get('target_folder', '/').strip()

            if not material_id:
                return jsonify({"status": "error", "message": "缺少课件ID"}), 400

            conn = get_db()
            cursor = conn.cursor()

            # 验证课件是否属于当前教师
            cursor.execute("""
                SELECT file_path, folder_path FROM teacher_materials
                WHERE id = ? AND teacher_id = ? AND course_schedule_id IS NULL
            """, (material_id, session.get('teacher_id')))

            material = cursor.fetchone()
            if not material:
                conn.close()
                return jsonify({"status": "error", "message": "课件不存在或无权限"}), 404

            # 移动物理文件
            old_path = material['file_path']
            if old_path.startswith('/uploads/'):
                relative_path = old_path[9:]  # 去掉 '/uploads/' 前缀
                teacher_id = session.get('teacher_id')

                success, message, new_relative_path = move_physical_file(teacher_id, relative_path, target_folder)

                if not success:
                    conn.close()
                    return jsonify({"status": "error", "message": message}), 400

                # 更新数据库记录
                new_file_path = f"/uploads/{new_relative_path}"
                cursor.execute("""
                    UPDATE teacher_materials
                    SET file_path = ?, folder_path = ?
                    WHERE id = ?
                """, (new_file_path, target_folder, material_id))

                conn.commit()

            conn.close()

            return jsonify({"status": "success", "message": "课件移动成功"})

        except Exception as e:
            return jsonify({"status": "error", "message": f"移动失败: {str(e)}"}), 500
