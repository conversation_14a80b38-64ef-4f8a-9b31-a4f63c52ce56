"""
签到管理相关路由
"""
from flask import render_template, request, jsonify, session, make_response
from app.models.database import get_db
from app.utils.decorators import teacher_required
from datetime import datetime
import uuid
import io
import csv


def register_routes(bp):
    """注册签到管理相关路由"""
    
    @bp.route('/attendance')
    @teacher_required
    def attendance():
        """签到管理页面"""
        # 获取当前选中的课堂
        current_class_id = session.get('current_class_id')
        if not current_class_id:
            return render_template("teacher/attendance.html", current_page="attendance", current_class=None)

        conn = get_db()
        cursor = conn.cursor()

        # 获取当前课堂信息
        cursor.execute("""
            SELECT cs.id, c.name as course_name, c.code as course_code,
                   cl.name as classroom_name, cls.name as class_name,
                   cs.day_of_week, cs.start_time, cs.end_time, cs.status,
                   cs.start_datetime, cs.description
            FROM course_schedules cs
            JOIN courses c ON cs.course_id = c.id
            JOIN classrooms cl ON cs.classroom_id = cl.id
            JOIN classes cls ON cs.class_id = cls.id
            WHERE cs.id = ? AND cs.teacher_id = ?
        """, (current_class_id, session.get('teacher_id')))

        current_class = cursor.fetchone()
        if not current_class:
            conn.close()
            return render_template("teacher/attendance.html", current_page="attendance", current_class=None)

        # 获取学生签到情况
        cursor.execute("""
            SELECT s.student_id, s.name, s.gender,
                   CASE WHEN ca.status = 'present' THEN 1 ELSE 0 END as signed_in,
                   ca.signin_time, ca.status as attendance_status
            FROM students s
            LEFT JOIN class_attendance ca ON s.student_id = ca.student_id AND ca.course_schedule_id = ?
            WHERE s.class_id IN (SELECT class_id FROM course_schedules WHERE id = ?)
            ORDER BY s.name
        """, (current_class_id, current_class_id))

        students = cursor.fetchall()
        conn.close()

        return render_template("teacher/attendance.html",
                              current_page="attendance",
                              current_class=current_class,
                              students=students)

    @bp.route('/manual_checkin/<course_schedule_id>', methods=['POST'])
    @teacher_required
    def manual_checkin(course_schedule_id):
        """手动签到"""
        try:
            # 验证课程权限
            conn = get_db()
            cursor = conn.cursor()

            cursor.execute("""
                SELECT id FROM course_schedules
                WHERE id = ? AND teacher_id = ?
            """, (course_schedule_id, session.get('teacher_id')))

            if not cursor.fetchone():
                conn.close()
                return jsonify({"status": "error", "message": "无权限访问该课程"}), 403

            # 获取表单数据
            student_id = request.form.get('student_id')
            remark = request.form.get('remark', '')

            if not student_id:
                conn.close()
                return jsonify({"status": "error", "message": "请选择学生"}), 400

            # 检查学生是否已签到
            cursor.execute("""
                SELECT id FROM class_attendance
                WHERE course_schedule_id = ? AND student_id = ? AND status = 'present'
            """, (course_schedule_id, student_id))

            if cursor.fetchone():
                conn.close()
                return jsonify({"status": "error", "message": "该学生已经签到"}), 400

            # 更新或插入签到记录
            current_time = datetime.now().isoformat()

            # 先尝试更新现有记录
            cursor.execute("""
                UPDATE class_attendance
                SET status = 'present', signin_time = ?, remark = ?
                WHERE course_schedule_id = ? AND student_id = ?
            """, (current_time, remark, course_schedule_id, student_id))

            # 如果没有更新任何记录，则插入新记录
            if cursor.rowcount == 0:
                attendance_id = str(uuid.uuid4())
                cursor.execute("""
                    INSERT INTO class_attendance (
                        id, course_schedule_id, student_id, signin_time, status, remark, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    attendance_id,
                    course_schedule_id,
                    student_id,
                    current_time,
                    'present',
                    remark,
                    current_time
                ))

            conn.commit()
            conn.close()

            return jsonify({"status": "success", "message": "签到成功"})

        except Exception as e:
            return jsonify({"status": "error", "message": f"签到失败: {str(e)}"}), 500

    @bp.route('/get_attendance_status/<course_schedule_id>', methods=['GET'])
    @teacher_required
    def get_attendance_status(course_schedule_id):
        """获取签到状态"""
        try:
            # 验证课程权限
            conn = get_db()
            cursor = conn.cursor()

            cursor.execute("""
                SELECT id FROM course_schedules
                WHERE id = ? AND teacher_id = ?
            """, (course_schedule_id, session.get('teacher_id')))

            if not cursor.fetchone():
                conn.close()
                return jsonify({"status": "error", "message": "无权限访问该课程"}), 403

            # 获取学生签到情况
            cursor.execute("""
                SELECT s.student_id, s.name, s.gender,
                       CASE WHEN ca.status = 'present' THEN 1 ELSE 0 END as signed_in,
                       ca.signin_time, ca.status as attendance_status
                FROM students s
                LEFT JOIN class_attendance ca ON s.student_id = ca.student_id AND ca.course_schedule_id = ?
                WHERE s.class_id IN (SELECT class_id FROM course_schedules WHERE id = ?)
                ORDER BY s.name
            """, (course_schedule_id, course_schedule_id))

            students = cursor.fetchall()
            conn.close()

            # 转换为字典格式
            students_list = []
            for student in students:
                students_list.append({
                    "student_id": student[0],
                    "name": student[1],
                    "gender": student[2],
                    "signed_in": student[3],
                    "signin_time": student[4] or '',
                    "attendance_status": student[5] or 'absent'
                })

            return jsonify({"status": "success", "students": students_list})

        except Exception as e:
            return jsonify({"status": "error", "message": f"获取签到状态失败: {str(e)}"}), 500

    @bp.route('/export_attendance/<course_schedule_id>', methods=['GET'])
    @teacher_required
    def export_attendance(course_schedule_id):
        """导出考勤"""
        try:
            # 验证课程权限
            conn = get_db()
            cursor = conn.cursor()

            cursor.execute("""
                SELECT cs.id, c.name as course_name, c.code as course_code,
                       cl.name as classroom_name, cls.name as class_name,
                       cs.day_of_week, cs.start_time, cs.end_time
                FROM course_schedules cs
                JOIN courses c ON cs.course_id = c.id
                JOIN classrooms cl ON cs.classroom_id = cl.id
                JOIN classes cls ON cs.class_id = cls.id
                WHERE cs.id = ? AND cs.teacher_id = ?
            """, (course_schedule_id, session.get('teacher_id')))

            course_info = cursor.fetchone()
            if not course_info:
                conn.close()
                return jsonify({"status": "error", "message": "无权限访问该课程"}), 403

            # 获取学生签到情况
            cursor.execute("""
                SELECT s.student_id, s.name, s.gender,
                       CASE WHEN ca.status = 'present' THEN '已签到' ELSE '未签到' END as status_text,
                       ca.signin_time, ca.remark
                FROM students s
                LEFT JOIN class_attendance ca ON s.student_id = ca.student_id AND ca.course_schedule_id = ?
                WHERE s.class_id IN (SELECT class_id FROM course_schedules WHERE id = ?)
                ORDER BY s.name
            """, (course_schedule_id, course_schedule_id))

            students = cursor.fetchall()
            conn.close()

            # 生成CSV内容
            output = io.StringIO()
            writer = csv.writer(output)

            # 写入标题行
            writer.writerow(['课程信息'])
            writer.writerow(['课程名称', course_info[1]])
            writer.writerow(['课程代码', course_info[2]])
            writer.writerow(['教室', course_info[3]])
            writer.writerow(['班级', course_info[4]])
            writer.writerow(['时间', f"{course_info[5]} {course_info[6]}-{course_info[7]}"])
            writer.writerow([])  # 空行

            # 写入考勤数据标题
            writer.writerow(['学号', '姓名', '性别', '签到状态', '签到时间', '备注'])

            # 写入学生数据
            for student in students:
                signin_time = student[4][:19] if student[4] else ''
                writer.writerow([
                    student[0],  # 学号
                    student[1],  # 姓名
                    student[2],  # 性别
                    student[3],  # 签到状态
                    signin_time,  # 签到时间
                    student[5] or ''  # 备注
                ])

            # 创建响应
            response = make_response(output.getvalue())
            response.headers['Content-Type'] = 'text/csv; charset=utf-8-sig'
            response.headers['Content-Disposition'] = f'attachment; filename=attendance_{course_info[2]}_{course_info[5]}.csv'

            return response

        except Exception as e:
            return jsonify({"status": "error", "message": f"导出失败: {str(e)}"}), 500
