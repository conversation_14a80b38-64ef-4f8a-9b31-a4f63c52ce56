"""
习题管理相关路由
"""
from flask import render_template, request, jsonify, session
from app.models.database import get_db
from app.utils.decorators import teacher_required
from datetime import datetime
import uuid
import json


def register_routes(bp):
    """注册习题管理相关路由"""
    
    @bp.route('/exercises')
    @teacher_required
    def exercises():
        """习题管理页面"""
        return render_template("teacher/exercises.html", current_page="exercises")

    @bp.route('/api/exercise_count')
    @teacher_required
    def api_exercise_count():
        """获取习题数量"""
        conn = get_db()
        cursor = conn.cursor()

        cursor.execute("SELECT COUNT(*) FROM exercises WHERE teacher_id = ?", (session.get('teacher_id'),))
        count = cursor.fetchone()[0]

        conn.close()
        return jsonify({"status": "success", "count": count})

    @bp.route('/save_exercise', methods=['POST'])
    @teacher_required
    def save_exercise():
        """保存习题"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({"status": "error", "message": "无效的请求数据"}), 400

            # 验证必要字段
            required_fields = ['type', 'question', 'difficulty', 'answer']
            for field in required_fields:
                if field not in data or not data[field]:
                    return jsonify({"status": "error", "message": f"缺少必要字段: {field}"}), 400

            conn = get_db()
            cursor = conn.cursor()

            # 生成习题ID
            exercise_id = str(uuid.uuid4())
            teacher_id = session.get('teacher_id')
            current_time = datetime.now().isoformat()

            # 处理选项数据
            options_json = None
            if data['type'] in ['single', 'multiple', 'judge']:
                options_json = json.dumps(data.get('options', []), ensure_ascii=False)

            # 插入习题数据
            cursor.execute("""
                INSERT INTO exercises (id, teacher_id, type, question, options, answer, difficulty, folder_path, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                exercise_id,
                teacher_id,
                data['type'],
                data['question'],
                options_json,
                data['answer'],
                data['difficulty'],
                data.get('folder_path', '/'),
                current_time,
                current_time
            ))

            conn.commit()
            conn.close()

            return jsonify({"status": "success", "message": "习题保存成功", "exercise_id": exercise_id})

        except Exception as e:
            return jsonify({"status": "error", "message": f"保存失败: {str(e)}"}), 500

    @bp.route('/get_exercises', methods=['GET'])
    @teacher_required
    def get_exercises():
        """获取习题列表"""
        try:
            conn = get_db()
            cursor = conn.cursor()

            # 获取筛选参数
            keyword = request.args.get('keyword', '').strip()
            types = request.args.get('types', '').strip()
            difficulties = request.args.get('difficulties', '').strip()

            # 构建基础查询
            base_query = """
                SELECT id, type, question, options, answer, difficulty, created_at, updated_at
                FROM exercises
                WHERE teacher_id = ?
            """
            params = [session.get('teacher_id')]

            # 添加搜索条件
            if keyword:
                base_query += " AND question LIKE ?"
                params.append(f'%{keyword}%')

            # 添加题型筛选
            if types:
                type_list = types.split(',')
                placeholders = ','.join(['?' for _ in type_list])
                base_query += f" AND type IN ({placeholders})"
                params.extend(type_list)

            # 添加难度筛选
            if difficulties:
                difficulty_list = difficulties.split(',')
                placeholders = ','.join(['?' for _ in difficulty_list])
                base_query += f" AND difficulty IN ({placeholders})"
                params.extend(difficulty_list)

            # 添加排序
            base_query += " ORDER BY created_at DESC"

            # 执行查询
            cursor.execute(base_query, params)

            exercises = []
            for row in cursor.fetchall():
                exercise = {
                    'id': row['id'],
                    'type': row['type'],
                    'question': row['question'],
                    'difficulty': row['difficulty'],
                    'created_at': row['created_at'],
                    'updated_at': row['updated_at']
                }

                # 解析选项数据
                if row['options']:
                    try:
                        exercise['options'] = json.loads(row['options'])
                    except:
                        exercise['options'] = []
                else:
                    exercise['options'] = []

                exercise['answer'] = row['answer']
                exercises.append(exercise)

            conn.close()
            return jsonify({"status": "success", "exercises": exercises})

        except Exception as e:
            return jsonify({"status": "error", "message": f"获取习题失败: {str(e)}"}), 500

    @bp.route('/get_exercise/<exercise_id>', methods=['GET'])
    @teacher_required
    def get_exercise(exercise_id):
        """获取单个习题详情"""
        try:
            conn = get_db()
            cursor = conn.cursor()

            # 查询习题详情，验证权限
            cursor.execute("""
                SELECT id, type, question, options, answer, difficulty, folder_path, created_at, updated_at
                FROM exercises
                WHERE id = ? AND teacher_id = ?
            """, (exercise_id, session.get('teacher_id')))

            exercise_row = cursor.fetchone()
            if not exercise_row:
                conn.close()
                return jsonify({"status": "error", "message": "习题不存在或无权限访问"}), 404

            # 构建习题数据
            exercise = {
                'id': exercise_row['id'],
                'type': exercise_row['type'],
                'question': exercise_row['question'],
                'difficulty': exercise_row['difficulty'],
                'folder_path': exercise_row['folder_path'],
                'created_at': exercise_row['created_at'],
                'updated_at': exercise_row['updated_at']
            }

            # 解析选项数据
            if exercise_row['options']:
                try:
                    exercise['options'] = json.loads(exercise_row['options'])
                except:
                    exercise['options'] = []
            else:
                exercise['options'] = []

            exercise['answer'] = exercise_row['answer']

            conn.close()
            return jsonify({"status": "success", "exercise": exercise})

        except Exception as e:
            return jsonify({"status": "error", "message": f"获取习题详情失败: {str(e)}"}), 500

    @bp.route('/update_exercise/<exercise_id>', methods=['PUT'])
    @teacher_required
    def update_exercise(exercise_id):
        """更新习题"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({"status": "error", "message": "无效的请求数据"}), 400

            # 验证必要字段
            required_fields = ['type', 'question', 'difficulty', 'answer']
            for field in required_fields:
                if field not in data or not data[field]:
                    return jsonify({"status": "error", "message": f"缺少必要字段: {field}"}), 400

            conn = get_db()
            cursor = conn.cursor()

            # 验证习题存在且有权限
            cursor.execute("""
                SELECT id FROM exercises
                WHERE id = ? AND teacher_id = ?
            """, (exercise_id, session.get('teacher_id')))

            if not cursor.fetchone():
                conn.close()
                return jsonify({"status": "error", "message": "习题不存在或无权限修改"}), 404

            # 处理选项数据
            options_json = None
            if data['type'] in ['single', 'multiple', 'judge']:
                options_json = json.dumps(data.get('options', []), ensure_ascii=False)

            current_time = datetime.now().isoformat()

            # 更新习题数据
            cursor.execute("""
                UPDATE exercises
                SET type = ?, question = ?, options = ?, answer = ?, difficulty = ?, folder_path = ?, updated_at = ?
                WHERE id = ? AND teacher_id = ?
            """, (
                data['type'],
                data['question'],
                options_json,
                data['answer'],
                data['difficulty'],
                data.get('folder_path', '/'),
                current_time,
                exercise_id,
                session.get('teacher_id')
            ))

            conn.commit()
            conn.close()

            return jsonify({"status": "success", "message": "习题更新成功"})

        except Exception as e:
            return jsonify({"status": "error", "message": f"更新失败: {str(e)}"}), 500

    @bp.route('/delete_exercise/<exercise_id>', methods=['DELETE'])
    @teacher_required
    def delete_exercise(exercise_id):
        """删除习题"""
        try:
            conn = get_db()
            cursor = conn.cursor()

            # 验证习题存在且有权限
            cursor.execute("""
                SELECT id FROM exercises
                WHERE id = ? AND teacher_id = ?
            """, (exercise_id, session.get('teacher_id')))

            if not cursor.fetchone():
                conn.close()
                return jsonify({"status": "error", "message": "习题不存在或无权限删除"}), 404

            # 删除习题
            cursor.execute("""
                DELETE FROM exercises
                WHERE id = ? AND teacher_id = ?
            """, (exercise_id, session.get('teacher_id')))

            conn.commit()
            conn.close()

            return jsonify({"status": "success", "message": "习题删除成功"})

        except Exception as e:
            return jsonify({"status": "error", "message": f"删除失败: {str(e)}"}), 500
