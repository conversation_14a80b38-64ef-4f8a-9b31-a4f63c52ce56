"""
分组管理相关路由
"""
from flask import render_template, request, jsonify, session
from app.models.database import get_db
from app.utils.decorators import teacher_required
from datetime import datetime
import uuid
import random


def register_routes(bp):
    """注册分组管理相关路由"""
    
    @bp.route('/groups')
    @teacher_required
    def groups():
        """小组管理页面"""
        # 获取当前选中的课堂
        current_class_id = session.get('current_class_id')
        if not current_class_id:
            return render_template("teacher/groups.html", current_page="groups", current_class=None)

        conn = get_db()
        cursor = conn.cursor()

        # 获取当前课堂信息
        cursor.execute("""
            SELECT cs.id, c.name as course_name, c.code as course_code,
                   cl.name as classroom_name, cls.name as class_name,
                   cs.day_of_week, cs.start_time, cs.end_time, cs.status,
                   cs.start_datetime, cs.description
            FROM course_schedules cs
            JOIN courses c ON cs.course_id = c.id
            JOIN classrooms cl ON cs.classroom_id = cl.id
            JOIN classes cls ON cs.class_id = cls.id
            WHERE cs.id = ? AND cs.teacher_id = ?
        """, (current_class_id, session.get('teacher_id')))

        current_class = cursor.fetchone()
        if not current_class:
            conn.close()
            return render_template("teacher/groups.html", current_page="groups", current_class=None)

        # 获取小组信息
        cursor.execute("""
            SELECT cg.id, cg.group_name, cg.group_description as description,
                   cg.created_at, COUNT(gm.student_id) as member_count
            FROM class_groups cg
            LEFT JOIN group_members gm ON cg.id = gm.group_id
            WHERE cg.course_schedule_id = ?
            GROUP BY cg.id, cg.group_name, cg.group_description, cg.created_at
            ORDER BY cg.created_at
        """, (current_class_id,))

        groups_data = cursor.fetchall()

        # 获取每个小组的成员信息
        groups = []
        for group in groups_data:
            cursor.execute("""
                SELECT gm.student_id, s.name, s.gender, 0 as is_leader
                FROM group_members gm
                JOIN students s ON gm.student_id = s.student_id
                WHERE gm.group_id = ?
                ORDER BY s.name
            """, (group['id'],))

            members = cursor.fetchall()

            groups.append({
                'id': group['id'],
                'group_name': group['group_name'],
                'description': group['description'],
                'created_at': group['created_at'],
                'members': [dict(member) for member in members]
            })

        conn.close()

        return render_template("teacher/groups.html",
                              current_page="groups",
                              current_class=current_class,
                              groups=groups)

    @bp.route('/auto_group/<course_schedule_id>', methods=['POST'])
    @teacher_required
    def auto_group(course_schedule_id):
        """自动分组"""
        try:
            # 验证课程权限
            conn = get_db()
            cursor = conn.cursor()

            cursor.execute("""
                SELECT id FROM course_schedules
                WHERE id = ? AND teacher_id = ?
            """, (course_schedule_id, session.get('teacher_id')))

            if not cursor.fetchone():
                conn.close()
                return jsonify({"status": "error", "message": "无权限访问该课程"}), 403

            # 获取表单数据
            method = request.form.get('method')
            group_count = request.form.get('group_count', type=int)
            group_size = request.form.get('group_size', type=int)
            strategy = request.form.get('strategy', 'random')

            # 获取该班级的所有学生
            cursor.execute("""
                SELECT s.student_id, s.name
                FROM students s
                JOIN course_schedules cs ON s.class_id = cs.class_id
                WHERE cs.id = ?
                ORDER BY s.name
            """, (course_schedule_id,))

            students = cursor.fetchall()

            if len(students) == 0:
                conn.close()
                return jsonify({"status": "error", "message": "该班级没有学生"}), 400

            # 清除现有分组
            cursor.execute("""
                DELETE FROM group_members WHERE group_id IN (
                    SELECT id FROM class_groups WHERE course_schedule_id = ?
                )
            """, (course_schedule_id,))

            cursor.execute("DELETE FROM class_groups WHERE course_schedule_id = ?", (course_schedule_id,))

            # 计算分组
            student_list = list(students)

            if strategy == 'random':
                random.shuffle(student_list)

            if method == 'by_count':
                if not group_count or group_count < 2:
                    conn.close()
                    return jsonify({"status": "error", "message": "组数必须大于等于2"}), 400

                students_per_group = len(student_list) // group_count
                remainder = len(student_list) % group_count

            elif method == 'by_size':
                if not group_size or group_size < 2:
                    conn.close()
                    return jsonify({"status": "error", "message": "每组人数必须大于等于2"}), 400

                group_count = (len(student_list) + group_size - 1) // group_size
                students_per_group = group_size
                remainder = 0

            # 创建分组
            current_time = datetime.now().isoformat()
            group_index = 0

            for i in range(group_count):
                group_id = str(uuid.uuid4())
                group_name = f"第{i+1}组"

                # 创建分组
                cursor.execute("""
                    INSERT INTO class_groups (id, course_schedule_id, group_name, group_description, created_by, created_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (group_id, course_schedule_id, group_name, "自动分组", session.get('teacher_id'), current_time))

                # 分配学生
                current_group_size = students_per_group + (1 if i < remainder else 0)

                for j in range(current_group_size):
                    if group_index < len(student_list):
                        student = student_list[group_index]
                        member_id = str(uuid.uuid4())

                        cursor.execute("""
                            INSERT INTO group_members (id, group_id, student_id, joined_at)
                            VALUES (?, ?, ?, ?)
                        """, (member_id, group_id, student['student_id'], current_time))

                        group_index += 1

            conn.commit()
            conn.close()

            return jsonify({"status": "success", "message": f"自动分组成功，共创建{group_count}个小组"})

        except Exception as e:
            if conn:
                conn.close()
            return jsonify({"status": "error", "message": f"自动分组失败: {str(e)}"}), 500

    @bp.route('/get_students/<course_schedule_id>', methods=['GET'])
    @teacher_required
    def get_students(course_schedule_id):
        """获取课程的学生列表"""
        try:
            conn = get_db()
            cursor = conn.cursor()

            # 验证课程权限
            cursor.execute("""
                SELECT id FROM course_schedules
                WHERE id = ? AND teacher_id = ?
            """, (course_schedule_id, session.get('teacher_id')))

            if not cursor.fetchone():
                conn.close()
                return jsonify({"status": "error", "message": "无权限访问该课程"}), 403

            # 获取学生列表
            cursor.execute("""
                SELECT s.student_id, s.name, s.gender
                FROM students s
                JOIN course_schedules cs ON s.class_id = cs.class_id
                WHERE cs.id = ?
                ORDER BY s.name
            """, (course_schedule_id,))

            students = cursor.fetchall()
            conn.close()

            students_list = [dict(student) for student in students]

            return jsonify({"status": "success", "students": students_list})

        except Exception as e:
            return jsonify({"status": "error", "message": f"获取学生列表失败: {str(e)}"}), 500

    @bp.route('/groups/<course_schedule_id>', methods=['GET'])
    @teacher_required
    def get_groups(course_schedule_id):
        """获取指定课程的分组数据（JSON格式）"""
        try:
            conn = get_db()
            cursor = conn.cursor()

            # 验证课程权限
            cursor.execute("""
                SELECT id FROM course_schedules
                WHERE id = ? AND teacher_id = ?
            """, (course_schedule_id, session.get('teacher_id')))

            if not cursor.fetchone():
                conn.close()
                return jsonify({"status": "error", "message": "无权限访问该课程"}), 403

            # 获取小组信息
            cursor.execute("""
                SELECT cg.id, cg.group_name, cg.group_description,
                       cg.created_at, COUNT(gm.student_id) as member_count
                FROM class_groups cg
                LEFT JOIN group_members gm ON cg.id = gm.group_id
                WHERE cg.course_schedule_id = ?
                GROUP BY cg.id, cg.group_name, cg.group_description, cg.created_at
                ORDER BY cg.created_at
            """, (course_schedule_id,))

            groups_data = cursor.fetchall()

            # 获取每个小组的成员信息
            groups = []
            for group in groups_data:
                cursor.execute("""
                    SELECT gm.student_id, s.name, s.gender, 0 as is_leader
                    FROM group_members gm
                    JOIN students s ON gm.student_id = s.student_id
                    WHERE gm.group_id = ?
                    ORDER BY s.name
                """, (group['id'],))

                members = cursor.fetchall()

                groups.append({
                    'id': group['id'],
                    'group_name': group['group_name'],
                    'group_description': group['group_description'],
                    'created_at': group['created_at'],
                    'members': [dict(member) for member in members]
                })

            conn.close()

            return jsonify({"status": "success", "groups": groups})

        except Exception as e:
            return jsonify({"status": "error", "message": f"获取分组数据失败: {str(e)}"}), 500

    @bp.route('/create_group', methods=['POST'])
    @teacher_required
    def create_group():
        """创建新的分组"""
        try:
            data = request.get_json()
            course_schedule_id = data.get('course_schedule_id')
            group_name = data.get('group_name')
            group_description = data.get('group_description', '')

            if not course_schedule_id or not group_name:
                return jsonify({"status": "error", "message": "课程ID和分组名称不能为空"}), 400

            conn = get_db()
            cursor = conn.cursor()

            # 验证课程权限
            cursor.execute("""
                SELECT id FROM course_schedules
                WHERE id = ? AND teacher_id = ?
            """, (course_schedule_id, session.get('teacher_id')))

            if not cursor.fetchone():
                conn.close()
                return jsonify({"status": "error", "message": "无权限访问该课程"}), 403

            # 检查分组名称是否已存在
            cursor.execute("""
                SELECT id FROM class_groups
                WHERE course_schedule_id = ? AND group_name = ?
            """, (course_schedule_id, group_name))

            if cursor.fetchone():
                conn.close()
                return jsonify({"status": "error", "message": "分组名称已存在"}), 400

            # 创建分组
            group_id = str(uuid.uuid4())
            current_time = datetime.now().isoformat()

            cursor.execute("""
                INSERT INTO class_groups (id, course_schedule_id, group_name, group_description, created_by, created_at)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (group_id, course_schedule_id, group_name, group_description, session.get('teacher_id'), current_time))

            conn.commit()
            conn.close()

            return jsonify({"status": "success", "message": "分组创建成功", "group_id": group_id})

        except Exception as e:
            return jsonify({"status": "error", "message": f"创建分组失败: {str(e)}"}), 500

    @bp.route('/add_to_group', methods=['POST'])
    @teacher_required
    def add_to_group():
        """添加学生到分组"""
        try:
            data = request.get_json()
            student_id = data.get('student_id')
            group_id = data.get('group_id')

            if not student_id or not group_id:
                return jsonify({"status": "error", "message": "学生ID和分组ID不能为空"}), 400

            conn = get_db()
            cursor = conn.cursor()

            # 验证分组权限
            cursor.execute("""
                SELECT cg.id FROM class_groups cg
                JOIN course_schedules cs ON cg.course_schedule_id = cs.id
                WHERE cg.id = ? AND cs.teacher_id = ?
            """, (group_id, session.get('teacher_id')))

            if not cursor.fetchone():
                conn.close()
                return jsonify({"status": "error", "message": "无权限访问该分组"}), 403

            # 检查学生是否已在该分组中
            cursor.execute("""
                SELECT id FROM group_members
                WHERE group_id = ? AND student_id = ?
            """, (group_id, student_id))

            if cursor.fetchone():
                conn.close()
                return jsonify({"status": "success", "message": "学生已在该分组中"})

            # 检查学生是否在其他分组中（同一课程）
            cursor.execute("""
                SELECT gm.group_id FROM group_members gm
                JOIN class_groups cg ON gm.group_id = cg.id
                JOIN class_groups target_cg ON target_cg.id = ? AND target_cg.course_schedule_id = cg.course_schedule_id
                WHERE gm.student_id = ?
            """, (group_id, student_id))

            existing_group = cursor.fetchone()
            if existing_group:
                # 从原分组中移除
                cursor.execute("""
                    DELETE FROM group_members
                    WHERE group_id = ? AND student_id = ?
                """, (existing_group['group_id'], student_id))

            # 添加到新分组
            member_id = str(uuid.uuid4())
            current_time = datetime.now().isoformat()

            cursor.execute("""
                INSERT INTO group_members (id, group_id, student_id, joined_at)
                VALUES (?, ?, ?, ?)
            """, (member_id, group_id, student_id, current_time))

            conn.commit()
            conn.close()

            return jsonify({"status": "success", "message": "添加成功"})

        except Exception as e:
            return jsonify({"status": "error", "message": f"添加失败: {str(e)}"}), 500

    @bp.route('/remove_from_group', methods=['POST'])
    @teacher_required
    def remove_from_group():
        """从分组中移除学生"""
        try:
            data = request.get_json()
            student_id = data.get('student_id')

            if not student_id:
                return jsonify({"status": "error", "message": "学生ID不能为空"}), 400

            conn = get_db()
            cursor = conn.cursor()

            # 查找学生所在的分组，并验证权限
            cursor.execute("""
                SELECT gm.group_id FROM group_members gm
                JOIN class_groups cg ON gm.group_id = cg.id
                JOIN course_schedules cs ON cg.course_schedule_id = cs.id
                WHERE gm.student_id = ? AND cs.teacher_id = ?
            """, (student_id, session.get('teacher_id')))

            group_member = cursor.fetchone()
            if not group_member:
                conn.close()
                return jsonify({"status": "error", "message": "学生不在任何分组中或无权限操作"}), 404

            # 从分组中移除学生
            cursor.execute("""
                DELETE FROM group_members
                WHERE student_id = ? AND group_id = ?
            """, (student_id, group_member['group_id']))

            conn.commit()
            conn.close()

            return jsonify({"status": "success", "message": "移除成功"})

        except Exception as e:
            return jsonify({"status": "error", "message": f"移除失败: {str(e)}"}), 500

    @bp.route('/delete_group/<group_id>', methods=['DELETE'])
    @teacher_required
    def delete_group(group_id):
        """删除指定分组"""
        try:
            conn = get_db()
            cursor = conn.cursor()

            # 验证分组权限
            cursor.execute("""
                SELECT cg.id FROM class_groups cg
                JOIN course_schedules cs ON cg.course_schedule_id = cs.id
                WHERE cg.id = ? AND cs.teacher_id = ?
            """, (group_id, session.get('teacher_id')))

            if not cursor.fetchone():
                conn.close()
                return jsonify({"status": "error", "message": "分组不存在或无权限删除"}), 404

            # 删除分组成员
            cursor.execute("DELETE FROM group_members WHERE group_id = ?", (group_id,))

            # 删除分组
            cursor.execute("DELETE FROM class_groups WHERE id = ?", (group_id,))

            conn.commit()
            conn.close()

            return jsonify({"status": "success", "message": "分组删除成功"})

        except Exception as e:
            return jsonify({"status": "error", "message": f"删除分组失败: {str(e)}"}), 500

    @bp.route('/clear_groups/<course_schedule_id>', methods=['POST'])
    @teacher_required
    def clear_groups(course_schedule_id):
        """清空所有分组"""
        try:
            conn = get_db()
            cursor = conn.cursor()

            # 验证课程权限
            cursor.execute("""
                SELECT id FROM course_schedules
                WHERE id = ? AND teacher_id = ?
            """, (course_schedule_id, session.get('teacher_id')))

            if not cursor.fetchone():
                conn.close()
                return jsonify({"status": "error", "message": "无权限访问该课程"}), 403

            # 删除分组成员
            cursor.execute("""
                DELETE FROM group_members WHERE group_id IN (
                    SELECT id FROM class_groups WHERE course_schedule_id = ?
                )
            """, (course_schedule_id,))

            # 删除分组
            cursor.execute("DELETE FROM class_groups WHERE course_schedule_id = ?", (course_schedule_id,))

            conn.commit()
            conn.close()

            return jsonify({"status": "success", "message": "所有分组已清空"})

        except Exception as e:
            return jsonify({"status": "error", "message": f"清空分组失败: {str(e)}"}), 500
