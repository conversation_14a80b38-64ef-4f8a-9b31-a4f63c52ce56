"""
试卷管理相关路由
"""
from flask import render_template, request, jsonify, session, redirect, url_for
from app.models.database import get_db
from app.utils.decorators import teacher_required
from datetime import datetime
import uuid
import json


def register_routes(bp):
    """注册试卷管理相关路由"""
    
    @bp.route('/papers')
    @teacher_required
    def papers():
        """试卷管理页面"""
        return render_template("teacher/papers.html", current_page="papers")

    @bp.route('/get_papers', methods=['GET'])
    @teacher_required
    def get_papers():
        """获取试卷列表"""
        try:
            conn = get_db()
            cursor = conn.cursor()

            # 获取筛选参数
            keyword = request.args.get('keyword', '').strip()
            resource_only = request.args.get('resource_only', '').lower() == 'true'

            if resource_only:
                # 资源库页面：获取所有试卷资源（包括原始试卷和作业副本）
                base_query = """
                    SELECT id, title, description, created_at, NULL as course_schedule_id,
                           '试卷' as type, '资源库' as source
                    FROM papers
                    WHERE teacher_id = ?
                    UNION ALL
                    SELECT h.id, h.title, h.description, h.created_at, h.course_schedule_id,
                           '作业' as type,
                           c.name || ' - ' || cls.name as source
                    FROM homework h
                    LEFT JOIN course_schedules cs ON h.course_schedule_id = cs.id
                    LEFT JOIN courses c ON cs.course_id = c.id
                    LEFT JOIN classes cls ON cs.class_id = cls.id
                    WHERE h.teacher_id = ?
                """
                params = [session.get('teacher_id'), session.get('teacher_id')]
            else:
                # 其他页面：只获取原始试卷
                base_query = """
                    SELECT id, title, description, created_at, NULL as course_schedule_id,
                           '试卷' as type,
                           '资源库' as source
                    FROM papers
                    WHERE teacher_id = ?
                """
                params = [session.get('teacher_id')]

            # 添加搜索条件
            if keyword:
                if resource_only:
                    base_query += " AND (title LIKE ? OR description LIKE ?)"
                    params.extend([f'%{keyword}%', f'%{keyword}%'])
                else:
                    base_query += " AND (title LIKE ? OR description LIKE ?)"
                    params.extend([f'%{keyword}%', f'%{keyword}%'])

            base_query += " ORDER BY created_at DESC"

            cursor.execute(base_query, params)
            papers = cursor.fetchall()

            # 转换为字典格式
            papers_list = []
            for paper in papers:
                papers_list.append({
                    'id': paper['id'],
                    'title': paper['title'],
                    'description': paper['description'],
                    'created_at': paper['created_at'],
                    'type': paper['type'],
                    'source': paper['source'],
                    'course_schedule_id': paper['course_schedule_id']
                })

            conn.close()
            return jsonify({"status": "success", "papers": papers_list})

        except Exception as e:
            return jsonify({"status": "error", "message": f"获取试卷失败: {str(e)}"}), 500

    @bp.route('/add_paper', methods=['GET', 'POST'])
    @teacher_required
    def add_paper():
        """新增试卷"""
        if request.method == 'POST':
            try:
                paper = request.get_json()
                if not paper:
                    return jsonify({"status": "error", "message": "无效的请求数据"}), 400

                # 生成唯一ID（如果没有提供）
                if 'id' not in paper:
                    paper['id'] = str(uuid.uuid4())

                conn = get_db()
                cursor = conn.cursor()

                # 试卷存储到资源库，不关联课程

                # 将整个试卷数据转换为JSON字符串存储到papers表
                cursor.execute(
                    "INSERT INTO papers (id, title, description, teacher_id, created_at, data) VALUES (?, ?, ?, ?, ?, ?)",
                    (
                        paper['id'],
                        paper.get('title', '未命名试卷'),
                        paper.get('description', ''),
                        session.get('teacher_id'),
                        datetime.now().isoformat(),
                        json.dumps(paper, ensure_ascii=False)
                    )
                )

                conn.commit()
                conn.close()

                return jsonify({"status": "success"})
            except Exception as e:
                return jsonify({"status": "error", "message": str(e)}), 500

        # GET 请求：处理课程ID参数
        course_id = request.args.get('course_id')
        conn = get_db()
        cursor = conn.cursor()

        # 如果提供了course_id，获取该课程的信息并验证权限
        current_course = None
        if course_id:
            cursor.execute("""
                SELECT cs.id, c.name as course_name, c.code as course_code,
                       cl.name as classroom_name, cls.name as class_name,
                       cs.day_of_week, cs.start_time, cs.end_time
                FROM course_schedules cs
                JOIN courses c ON cs.course_id = c.id
                JOIN classrooms cl ON cs.classroom_id = cl.id
                JOIN classes cls ON cs.class_id = cls.id
                WHERE cs.id = ? AND cs.teacher_id = ?
            """, (course_id, session.get('teacher_id')))
            current_course = cursor.fetchone()

        # 获取当前教师的所有课程安排（用于备选）
        cursor.execute("""
            SELECT cs.id, c.name as course_name, c.code as course_code,
                   cl.name as classroom_name, cls.name as class_name,
                   cs.day_of_week, cs.start_time, cs.end_time
            FROM course_schedules cs
            JOIN courses c ON cs.course_id = c.id
            JOIN classrooms cl ON cs.classroom_id = cl.id
            JOIN classes cls ON cs.class_id = cls.id
            WHERE cs.teacher_id = ?
            ORDER BY c.name, cs.day_of_week, cs.start_time
        """, (session.get('teacher_id'),))

        course_schedules = cursor.fetchall()
        conn.close()

        return render_template("teacher/add_paper.html",
                              course_schedules=course_schedules,
                              current_course=current_course,
                              current_page="add_paper")

    @bp.route('/paper/<paper_id>', methods=['GET'])
    @teacher_required
    def get_paper(paper_id):
        """获取单个试卷"""
        try:
            conn = get_db()
            cursor = conn.cursor()

            # 先尝试从papers表查询
            cursor.execute("SELECT data FROM papers WHERE id = ? AND teacher_id = ?", (paper_id, session.get('teacher_id')))
            paper_row = cursor.fetchone()

            # 如果papers表中没有，再从homework表查询
            if not paper_row:
                cursor.execute("SELECT data FROM homework WHERE id = ? AND teacher_id = ?", (paper_id, session.get('teacher_id')))
                paper_row = cursor.fetchone()

            conn.close()

            if not paper_row:
                return jsonify({"status": "error", "message": "试卷不存在"}), 404

            # 返回试卷数据
            paper = json.loads(paper_row[0])

            return jsonify(paper)
        except Exception as e:
            return jsonify({"status": "error", "message": str(e)}), 500

    @bp.route('/paper/<paper_id>', methods=['DELETE'])
    @teacher_required
    def delete_paper(paper_id):
        """删除试卷"""
        try:
            conn = get_db()
            cursor = conn.cursor()

            # 先检查papers表中是否存在
            cursor.execute("SELECT id FROM papers WHERE id = ? AND teacher_id = ?", (paper_id, session.get('teacher_id')))
            paper = cursor.fetchone()

            if paper:
                # 删除资源库试卷
                cursor.execute("DELETE FROM papers WHERE id = ?", (paper_id,))
                message = "试卷删除成功"
            else:
                # 检查homework表中是否存在
                cursor.execute("SELECT id FROM homework WHERE id = ? AND teacher_id = ?", (paper_id, session.get('teacher_id')))
                homework = cursor.fetchone()

                if homework:
                    # 删除课堂作业
                    cursor.execute("DELETE FROM homework WHERE id = ?", (paper_id,))
                    # 同时删除关联的成绩记录
                    cursor.execute("DELETE FROM homework_results WHERE homework_id = ?", (paper_id,))
                    message = "课堂作业删除成功"
                else:
                    conn.close()
                    return jsonify({"status": "error", "message": "试卷不存在或无权限删除"}), 404

            conn.commit()
            conn.close()

            return jsonify({"status": "success", "message": message})
        except Exception as e:
            return jsonify({"status": "error", "message": str(e)}), 500

    @bp.route('/api/paper_count')
    @teacher_required
    def api_paper_count():
        """获取试卷数量"""
        conn = get_db()
        cursor = conn.cursor()

        # 统计作业数量
        cursor.execute("""
            SELECT COUNT(*) as count
            FROM homework h
            JOIN course_schedules cs ON h.course_schedule_id = cs.id
            WHERE cs.teacher_id = ?
        """, (session.get('teacher_id'),))

        result = cursor.fetchone()
        count = result['count'] if result else 0

        conn.close()
        return jsonify({"status": "success", "count": count})
