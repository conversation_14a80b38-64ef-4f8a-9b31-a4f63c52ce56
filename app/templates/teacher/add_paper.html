{% extends "/teacher/base.html" %}

{% block title %}创建试卷{% endblock %}

{% block styles %}
<link rel="stylesheet" href="/static/css/add_paper.css">
<style>
.paper-type-info {
    margin-bottom: 20px;
    padding: 15px;
    border-radius: 6px;
    background: #f8f9fa;
    border-left: 4px solid #1E9FFF;
}

.type-badge {
    display: inline-flex;
    align-items: center;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 10px;
}

.type-badge.course-paper {
    background: #e8f4fd;
    color: #1E9FFF;
}

.type-badge.resource-paper {
    background: #f0f9ff;
    color: #009688;
}

.type-badge i {
    margin-right: 6px;
    font-size: 16px;
}

.course-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.course-name {
    font-size: 16px;
    font-weight: 500;
    color: #333;
}

.class-name {
    font-size: 14px;
    color: #666;
}

.resource-info .resource-desc {
    font-size: 14px;
    color: #666;
    line-height: 1.5;
}
</style>
{% endblock %}

{% block content %}
            <div class="layui-fluid">
                <div class="layui-row layui-col-space15">
                    <!-- 左侧菜单栏 -->
                    <div class="layui-col-md3 sidebar-fixed">
                        <div class="layui-card">
                            <div class="layui-card-header">题目类型</div>
                            <div class="layui-card-body">
                                <div class="question-type-buttons">
                                    <div class="button-row">
                                        <button type="button" class="layui-btn layui-btn-primary question-type-btn" onclick="addQuestion('single')">
                                            <i class="layui-icon layui-icon-radio"></i>
                                            单选题
                                        </button>
                                        <button type="button" class="layui-btn layui-btn-primary question-type-btn" onclick="addQuestion('multiple')">
                                            <i class="layui-icon layui-icon-checkbox"></i>
                                            多选题
                                        </button>
                                    </div>
                                    <div class="button-row">
                                        <button type="button" class="layui-btn layui-btn-primary question-type-btn" onclick="addQuestion('judge')">
                                            <i class="layui-icon layui-icon-help"></i>
                                            判断题
                                        </button>
                                        <button type="button" class="layui-btn layui-btn-primary question-type-btn" onclick="addQuestion('fill')">
                                            <i class="layui-icon layui-icon-edit"></i>
                                            填空题
                                        </button>
                                    </div>
                                    <div class="button-row single-button">
                                        <button type="button" class="layui-btn layui-btn-primary question-type-btn" onclick="addQuestion('subjective')">
                                            <i class="layui-icon layui-icon-form"></i>
                                            主观题
                                        </button>
                                    </div>
                                    <div class="button-row single-button import-row">
                                        <button type="button" class="layui-btn layui-btn-normal question-type-btn import-btn" onclick="openImportExerciseModal()">
                                            <i class="layui-icon layui-icon-download-circle"></i>
                                            导入习题
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 中间内容区域 -->
                    <div class="layui-col-md6 content-fixed">
                        <div class="layui-card content-card">
                            <div class="layui-card-header paper-info-header">
                                <!-- 显示试卷类型信息 -->
                                <div class="paper-type-info">
                                    <div class="type-badge resource-paper">
                                        <i class="layui-icon layui-icon-file"></i>
                                        <span>试卷</span>
                                    </div>
                                    <div class="resource-info">
                                        <span class="resource-desc">试卷将保存到资源库</span>
                                    </div>
                                    <input type="hidden" id="course-schedule" value="">
                                </div>
                                <div class="paper-basic-info">
                                    <div class="layui-form-item">
                                        <input type="text" id="paper-title" lay-verify="required" placeholder="试卷标题" autocomplete="off" class="layui-input">
                                    </div>
                                    <div class="layui-form-item">
                                        <textarea id="paper-description" placeholder="试卷描述（可选）" class="layui-textarea" rows="2"></textarea>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-card-body content-scrollable">
                                <div id="questions">
                                    <p id="no-questions-message" style="color: gray; text-align: center;">点击左侧按钮添加题目</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧设置区域 -->
                    <div class="layui-col-md3 sidebar-fixed">
                        <div class="layui-card">
                            <div class="layui-card-header">
                                <i class="layui-icon layui-icon-set"></i>
                                考试设置
                            </div>
                            <div class="layui-card-body paper-settings">
                                <div class="setting-item">
                                    <div class="setting-label">
                                        <i class="layui-icon layui-icon-time"></i>
                                        考试时长
                                    </div>
                                    <div class="setting-input">
                                        <div class="time-input-group">
                                            <input type="number" id="paper-time" lay-verify="required|number" min="1" value="45" autocomplete="off" class="layui-input">
                                            <span class="time-unit">分钟</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="setting-item total-score-item">
                                    <div class="setting-label">
                                        <i class="layui-icon layui-icon-star"></i>
                                        试卷总分
                                    </div>
                                    <div class="total-score-display">
                                        <span id="total-score">0</span> 分
                                    </div>
                                </div>

                                <div class="setting-actions">
                                    <button type="button" class="layui-btn layui-btn-normal layui-btn-fluid save-paper-btn" onclick="submitPaper()">
                                        <i class="layui-icon layui-icon-ok"></i>
                                        保存
                                    </button>
                                    <button type="button" class="layui-btn layui-btn-primary layui-btn-fluid cancel-paper-btn" onclick="handleCancelEdit()">
                                        <i class="layui-icon layui-icon-close"></i>
                                        取消
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

<!-- 导入习题弹窗 -->
<div id="importExerciseModal" style="display: none; padding: 20px;">
    <div class="import-exercise-container">
        <!-- 搜索和筛选区域 -->
        <div class="search-filter-container">
            <!-- 搜索框 -->
            <div class="search-box">
                <div class="search-input-wrapper">
                    <div class="layui-input-group">
                        <div class="layui-input-prefix">
                            <i class="layui-icon layui-icon-search"></i>
                        </div>
                        <input type="text" id="importSearchInput" placeholder="搜索习题内容" class="layui-input">
                        <div class="layui-input-suffix">
                            <button type="button" id="importClearSearchBtn" class="clear-btn" style="display: none;">
                                <i class="layui-icon layui-icon-close"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <button type="button" id="importSearchBtn" class="search-btn">搜索</button>
            </div>

            <!-- 筛选按钮 -->
            <div class="filter-box">
                <div class="filter-dropdown">
                    <button type="button" class="filter-btn" id="importFilterBtn">
                        <i class="layui-icon layui-icon-screen"></i>
                        <span>筛选</span>
                        <span id="importFilterText">全部</span>
                        <i class="layui-icon layui-icon-down arrow-icon"></i>
                    </button>

                    <!-- 筛选下拉面板 -->
                    <div class="filter-panel" id="importFilterPanel" style="display: none;">
                        <!-- 题型筛选 -->
                        <div class="filter-section">
                            <div class="filter-label">题型：</div>
                            <div class="filter-options">
                                <label class="filter-option">
                                    <input type="checkbox" name="importTypeFilter" value="single">
                                    <span>单选题</span>
                                </label>
                                <label class="filter-option">
                                    <input type="checkbox" name="importTypeFilter" value="multiple">
                                    <span>多选题</span>
                                </label>
                                <label class="filter-option">
                                    <input type="checkbox" name="importTypeFilter" value="judge">
                                    <span>判断题</span>
                                </label>
                                <label class="filter-option">
                                    <input type="checkbox" name="importTypeFilter" value="fill">
                                    <span>填空题</span>
                                </label>
                                <label class="filter-option">
                                    <input type="checkbox" name="importTypeFilter" value="subjective">
                                    <span>主观题</span>
                                </label>
                            </div>
                        </div>

                        <!-- 难度筛选 -->
                        <div class="filter-section">
                            <div class="filter-label">难度：</div>
                            <div class="filter-options">
                                <label class="filter-option">
                                    <input type="checkbox" name="importDifficultyFilter" value="easy">
                                    <span>简单</span>
                                </label>
                                <label class="filter-option">
                                    <input type="checkbox" name="importDifficultyFilter" value="medium">
                                    <span>中等</span>
                                </label>
                                <label class="filter-option">
                                    <input type="checkbox" name="importDifficultyFilter" value="hard">
                                    <span>困难</span>
                                </label>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="filter-actions">
                            <button type="button" class="clear-filter-btn" id="importClearFilterBtn">清空条件</button>
                            <button type="button" class="confirm-filter-btn" id="importConfirmFilterBtn">确定</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 题目统计 -->
            <div class="question-count">
                <span id="importQuestionCountText">共 0 道题</span>
            </div>
        </div>

        <!-- 习题列表 -->
        <div class="import-exercise-list" id="importExerciseList">
            <table class="layui-table" lay-skin="line">
                <thead>
                    <tr>
                        <th width="50">
                            <input type="checkbox" id="selectAllExercises" lay-skin="primary">
                        </th>
                        <th width="80">序号</th>
                        <th width="100">题型</th>
                        <th>题目内容</th>
                        <th width="80">难度</th>
                        <th width="120">创建时间</th>
                    </tr>
                </thead>
                <tbody id="importExerciseTableBody">
                    <tr>
                        <td colspan="6" style="text-align: center; color: #999; padding: 40px;">
                            正在加载习题数据...
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 分页 -->
        <div id="importPagination" style="text-align: center; margin-top: 20px;"></div>

        <!-- 操作按钮 -->
        <div class="import-actions" style="text-align: center; margin-top: 20px; border-top: 1px solid #e6e6e6; padding-top: 20px;">
            <button type="button" class="layui-btn layui-btn-primary" onclick="layer.closeAll()">取消</button>
            <button type="button" class="layui-btn layui-btn-normal" id="confirmImportBtn">
                <i class="layui-icon layui-icon-ok"></i>
                导入选中习题
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="/static/js/add_paper.js"></script>
<script>
layui.use(['element', 'form'], function(){
    var element = layui.element;
    var form = layui.form;

    // 初始化导航菜单和表单验证
    element.init();
    form.render();
});
</script>
{% endblock %}