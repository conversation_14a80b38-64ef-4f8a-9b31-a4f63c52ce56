{% extends "/teacher/base.html" %}

{% block title %}作业分析{% endblock %}

{% block styles %}
<link rel="stylesheet" href="/static/css/analysis.css">
{% endblock %}

{% block content %}
                {% if current_course %}
                <!-- 显示当前课程信息 -->
                <div class="layui-card" style="margin-bottom: 15px;">
                    <div class="layui-card-header">
                        <i class="layui-icon layui-icon-read"></i>
                        课程：{{ current_course.course_name }}({{ current_course.course_code }}) - {{ current_course.class_name }}
                    </div>
                </div>
                {% endif %}

                <div class="layui-row layui-col-space15">
                    <div class="layui-col-md12">
                        <div class="layui-card">
                            <div class="layui-card-header">
                                作业分析 - {{ homework.title }}
                                {% if current_course %}
                                <span style="color: #999; font-size: 14px; margin-left: 10px;">
                                    ({{ current_course.course_name }})
                                </span>
                                {% endif %}
                            </div>
                            <div class="layui-card-body">
                                {% if results|length > 0 %}
                                    <div class="layui-tab">
                                        <ul class="layui-tab-title">
                                            <li class="layui-this">成绩统计</li>
                                            <li>题目分析</li>
                                            <li>错题统计</li>
                                        </ul>
                                        <div class="layui-tab-content">
                                            <!-- 成绩统计面板 -->
                                            <div class="layui-tab-item layui-show">
                                                <div class="layui-row layui-col-space15">
                                                    <div class="layui-col-md6">
                                                        <div class="layui-card">
                                                            <div class="layui-card-header">成绩分布</div>
                                                            <div class="layui-card-body">
                                                                <canvas id="scoreDistributionChart"></canvas>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="layui-col-md6">
                                                        <div class="layui-card">
                                                            <div class="layui-card-header">学生成绩</div>
                                                            <div class="layui-card-body">
                                                                <div class="chart-container"></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="layui-card">
                                                    <div class="layui-card-header">
                                                        统计指标
                                                        <div style="float: right;">
                                                            <button class="layui-btn layui-btn-sm layui-btn-normal" onclick="exportResults()">
                                                                <i class="layui-icon layui-icon-export"></i> 导出成绩
                                                            </button>
                                                        </div>
                                                    </div>
                                                    <div class="layui-card-body">
                                                        <div class="layui-row">
                                                            <div class="layui-col-md3">
                                                                <div class="layui-stat">
                                                                    <div class="layui-stat-title">平均分</div>
                                                                    <div class="layui-stat-number">{{ stats.avg_score|round(2) }}</div>
                                                                </div>
                                                            </div>
                                                            <div class="layui-col-md3">
                                                                <div class="layui-stat">
                                                                    <div class="layui-stat-title">最高分</div>
                                                                    <div class="layui-stat-number">{{ stats.max_score }}</div>
                                                                </div>
                                                            </div>
                                                            <div class="layui-col-md3">
                                                                <div class="layui-stat">
                                                                    <div class="layui-stat-title">最低分</div>
                                                                    <div class="layui-stat-number">{{ stats.min_score }}</div>
                                                                </div>
                                                            </div>
                                                            <div class="layui-col-md3">
                                                                <div class="layui-stat">
                                                                    <div class="layui-stat-title">及格率</div>
                                                                    <div class="layui-stat-number">{{ stats.pass_rate|round(2) }}%</div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="layui-row" style="margin-top: 20px;">
                                                            <div class="layui-col-md3">
                                                                <div class="layui-stat">
                                                                    <div class="layui-stat-title">参与人数</div>
                                                                    <div class="layui-stat-number">{{ results|length }}</div>
                                                                </div>
                                                            </div>
                                                            <div class="layui-col-md3">
                                                                <div class="layui-stat">
                                                                    <div class="layui-stat-title">优秀率(≥85)</div>
                                                                    <div class="layui-stat-number">{{ stats.excellent_rate|round(2) }}%</div>
                                                                </div>
                                                            </div>
                                                            <div class="layui-col-md3">
                                                                <div class="layui-stat">
                                                                    <div class="layui-stat-title">良好率(≥75)</div>
                                                                    <div class="layui-stat-number">{{ stats.good_rate|round(2) }}%</div>
                                                                </div>
                                                            </div>
                                                            <div class="layui-col-md3">
                                                                <div class="layui-stat">
                                                                    <div class="layui-stat-title">不及格率(<60)</div>
                                                                    <div class="layui-stat-number">{{ stats.fail_rate|round(2) }}%</div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- 学生答题详情 -->
                                                <div class="layui-card">
                                                    <div class="layui-card-header">
                                                        学生答题详情
                                                        <div style="float: right;">
                                                            <input type="text" id="studentSearchInput" placeholder="搜索学生姓名或学号" class="layui-input" style="width: 200px; display: inline-block;">
                                                            <button class="layui-btn layui-btn-sm" onclick="searchStudent()">搜索</button>
                                                        </div>
                                                    </div>
                                                    <div class="layui-card-body">
                                                        <table class="layui-table" lay-skin="line">
                                                            <thead>
                                                                <tr>
                                                                    <th width="80">排名</th>
                                                                    <th width="120">学号</th>
                                                                    <th width="100">姓名</th>
                                                                    <th width="80">得分</th>
                                                                    <th width="120">提交时间</th>
                                                                    <th width="100">用时</th>
                                                                    <th>操作</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody id="studentResultsTable">
                                                                {% for result in results %}
                                                                <tr>
                                                                    <td>{{ loop.index }}</td>
                                                                    <td>{{ result.student_id }}</td>
                                                                    <td>{{ result.student_name or '未知' }}</td>
                                                                    <td>
                                                                        <span class="score-badge score-{{ 'excellent' if result.score >= 85 else 'good' if result.score >= 75 else 'pass' if result.score >= 60 else 'fail' }}">
                                                                            {{ result.score }}
                                                                        </span>
                                                                    </td>
                                                                    <td>{{ result.submitted_at[:16] if result.submitted_at else '未知' }}</td>
                                                                    <td>{{ result.duration or '未知' }}</td>
                                                                    <td>
                                                                        <button class="layui-btn layui-btn-xs layui-btn-normal" onclick="viewStudentDetail('{{ result.student_id }}')">
                                                                            <i class="layui-icon layui-icon-eye"></i> 查看详情
                                                                        </button>
                                                                    </td>
                                                                </tr>
                                                                {% endfor %}
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- 题目分析面板 -->
                                            <div class="layui-tab-item">
                                                <div class="layui-card">
                                                    <div class="layui-card-header">题目正确率分析</div>
                                                    <div class="layui-card-body">
                                                        {% for question in question_stats %}
                                                        <div class="question-stat layui-row">
                                                            <div class="layui-col-md9">
                                                                <div class="question-text">
                                                                    <span class="layui-badge">{{ question.type_name }}</span>
                                                                    <span>{{ question.text }}</span>
                                                                </div>
                                                            </div>
                                                            <div class="layui-col-md3">
                                                                <div class="question-correct-rate">
                                                                    正确率: {{ question.correct_rate|round(2) }}%
                                                                    <div class="layui-progress">
                                                                        <div class="layui-progress-bar" lay-options="{percent: '{{ question.correct_rate }}%'}"></div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            {% if question.correct_answer %}
                                                            <div class="layui-col-md12 correct-answer">
                                                                <strong>答案:</strong> {{ question.correct_answer }}
                                                            </div>
                                                            {% endif %}
                                                        </div>
                                                        {% endfor %}
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- 错题统计面板 -->
                                            <div class="layui-tab-item">
                                                <div class="layui-card">
                                                    <div class="layui-card-header">
                                                        错题统计分析
                                                        <div style="float: right;">
                                                            <button class="layui-btn layui-btn-sm layui-btn-warm" onclick="exportWrongQuestions()">
                                                                <i class="layui-icon layui-icon-export"></i> 导出错题
                                                            </button>
                                                        </div>
                                                    </div>
                                                    <div class="layui-card-body">
                                                        <div class="wrong-questions-analysis">
                                                            {% for question in question_stats %}
                                                            {% if question.correct_rate < 60 %}
                                                            <div class="wrong-question-item">
                                                                <div class="question-header">
                                                                    <span class="layui-badge layui-bg-red">错误率高</span>
                                                                    <span class="question-type">{{ question.type_name }}</span>
                                                                    <span class="error-rate">错误率: {{ (100 - question.correct_rate)|round(2) }}%</span>
                                                                </div>
                                                                <div class="question-content">
                                                                    <p><strong>题目:</strong> {{ question.text }}</p>
                                                                    {% if question.correct_answer %}
                                                                    <p><strong>正确答案:</strong> {{ question.correct_answer }}</p>
                                                                    {% endif %}
                                                                </div>
                                                                <div class="question-stats">
                                                                    <div class="stat-item">
                                                                        <span class="stat-label">答对人数:</span>
                                                                        <span class="stat-value">{{ (question.correct_rate * results|length / 100)|round|int }}</span>
                                                                    </div>
                                                                    <div class="stat-item">
                                                                        <span class="stat-label">答错人数:</span>
                                                                        <span class="stat-value">{{ ((100 - question.correct_rate) * results|length / 100)|round|int }}</span>
                                                                    </div>
                                                                </div>
                                                                <div class="improvement-suggestion">
                                                                    <strong>改进建议:</strong>
                                                                    <span class="suggestion-text">
                                                                        {% if question.correct_rate < 30 %}
                                                                        此题错误率极高，建议重点讲解相关知识点，并安排专项练习。
                                                                        {% elif question.correct_rate < 50 %}
                                                                        此题有一定难度，建议课堂重点讲解，并提供更多练习机会。
                                                                        {% else %}
                                                                        此题需要适当讲解，帮助学生理解易错点。
                                                                        {% endif %}
                                                                    </span>
                                                                </div>
                                                            </div>
                                                            {% endif %}
                                                            {% endfor %}

                                                            {% if question_stats|selectattr('correct_rate', 'lt', 60)|list|length == 0 %}
                                                            <div class="no-wrong-questions">
                                                                <div style="text-align: center; padding: 40px; color: #5FB878;">
                                                                    <i class="layui-icon layui-icon-ok-circle" style="font-size: 48px; margin-bottom: 15px;"></i>
                                                                    <p>太棒了！所有题目的正确率都在60%以上</p>
                                                                    <p style="color: #999; font-size: 14px;">学生们掌握得很好，继续保持！</p>
                                                                </div>
                                                            </div>
                                                            {% endif %}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                {% else %}
                                    <div class="layui-empty">尚无学生答题数据</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // 初始化layui组件
    layui.use(['element', 'form', 'progress'], function(){
        var element = layui.element;
        var form = layui.form;
        var progress = layui.progress;

        // 初始化进度条
        progress.render();
    });

    // 传递成绩分布数据
    window.scoreDistributionData = {{ score_distribution|default([0, 0, 0, 0, 0, 0, 0, 0, 0, 0])|tojson|safe }};
    // 传递学生成绩数据
    window.studentResults = {{ results|default([])|tojson|safe }};
    // 传递题目统计数据
    window.questionStats = {{ question_stats|default([])|tojson|safe }};
</script>
<script src="/static/js/analysis.js"></script>
{% endblock %}