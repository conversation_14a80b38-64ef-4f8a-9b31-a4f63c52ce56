{% extends "teacher/base.html" %}
{% block title %}试卷管理{% endblock %}

{%block styles%}
<style>
/* 搜索筛选容器样式 */
.search-filter-container {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
}

/* 搜索框样式 */
.search-box {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
    max-width: 450px;
}

.search-input-wrapper {
    flex: 1;
}

.search-box .layui-input-group {
    border-radius: 6px;
    overflow: hidden;
    border: 1px solid #d9d9d9;
    background: #fff;
    display: flex;
    align-items: center;
}

.search-box .layui-input {
    border: none;
    padding: 8px 12px;
    font-size: 14px;
    flex: 1;
    outline: none;
}

.search-box .layui-input-prefix {
    padding: 0 12px;
    background: #fff;
    border: none;
    display: flex;
    align-items: center;
}

.search-box .layui-input-suffix {
    display: flex;
    align-items: center;
    padding: 0 8px;
    background: #fff;
}

.clear-btn {
    background: none;
    border: none;
    color: #999;
    padding: 4px;
    cursor: pointer;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.clear-btn:hover {
    background: #f0f0f0;
    color: #666;
}

.search-btn {
    background: #1E9FFF;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

.search-btn:hover {
    background: #1890ff;
}



/* 统计信息样式 */
.question-count {
    color: #666;
    font-size: 14px;
    white-space: nowrap;
}

/* 试卷列表样式 */
.paper-list {
    background: white;
    border-radius: 6px;
    overflow: hidden;
}

.layui-table {
    margin: 0;
}

.layui-table th {
    background: #fafafa;
    font-weight: 600;
    color: #333;
}

.layui-table td {
    border-bottom: 1px solid #f0f0f0;
}

.layui-table tbody tr:hover {
    background: #f8f9fa;
}

/* 操作按钮样式 */
.action-btn {
    padding: 4px 8px;
    margin: 0 2px;
    border-radius: 4px;
    font-size: 12px;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
}

.action-btn.view {
    background: #1E9FFF;
    color: white;
}

.action-btn.view:hover {
    background: #1890ff;
}

.action-btn.edit {
    background: #FF9800;
    color: white;
}

.action-btn.edit:hover {
    background: #f57c00;
}



.action-btn.delete {
    background: #F44336;
    color: white;
}

.action-btn.delete:hover {
    background: #d32f2f;
}
</style>
{% endblock %}

{% block content %}
<div class="content-card">
    <div class="layui-card-header">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <div>
                <h2>试卷管理</h2>
            </div>
            <div>
                <button type="button" class="layui-btn layui-btn-normal" onclick="location.href='/teacher/add_paper'">
                    <i class="layui-icon layui-icon-add-1"></i> 新建试卷
                </button>
            </div>
        </div>
    </div>
    <div class="layui-card-body">
        <!-- 搜索和筛选区域 -->
        <div class="search-filter-container">
            <!-- 搜索框 -->
            <div class="search-box">
                <div class="search-input-wrapper">
                    <div class="layui-input-group">
                        <div class="layui-input-prefix">
                            <i class="layui-icon layui-icon-search"></i>
                        </div>
                        <input type="text" id="searchInput" placeholder="搜索试卷标题或描述" class="layui-input">
                        <div class="layui-input-suffix">
                            <button type="button" id="clearSearchBtn" class="clear-btn" style="display: none;">
                                <i class="layui-icon layui-icon-close"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <button type="button" id="searchBtn" class="search-btn">搜索</button>
            </div>



            <!-- 试卷统计 -->
            <div class="question-count">
                <span id="paperCountText">共 0 份试卷</span>
            </div>
        </div>

        <!-- 试卷列表 -->
        <div class="paper-list" id="paperList">
            <table class="layui-table" lay-skin="line">
                <thead>
                    <tr>
                        <th width="80">序号</th>
                        <th>试卷标题</th>
                        <th width="100">类型</th>
                        <th width="150">来源</th>
                        <th width="200">描述</th>
                        <th width="120">创建时间</th>
                        <th width="150">操作</th>
                    </tr>
                </thead>
                <tbody id="paperTableBody">
                    <tr>
                        <td colspan="7" style="text-align: center; color: #999; padding: 40px;">
                            暂无试卷数据，点击"新建试卷"开始创建
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 分页 -->
        <div id="pagination" style="text-align: center; margin-top: 30px;"></div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
layui.use(['form', 'layer', 'laypage'], function(){
    var form = layui.form;
    var layer = layui.layer;
    var laypage = layui.laypage;

    // 全局变量存储当前筛选条件
    var currentFilters = {
        keyword: ''
    };

    // 搜索功能
    $('#searchBtn').click(function() {
        performSearch();
    });

    $('#searchInput').keypress(function(e) {
        if (e.which === 13) {
            performSearch();
        }
    });

    $('#searchInput').on('input', function() {
        var value = $(this).val();
        if (value) {
            $('#clearSearchBtn').show();
        } else {
            $('#clearSearchBtn').hide();
        }
    });

    $('#clearSearchBtn').click(function() {
        $('#searchInput').val('');
        $(this).hide();
        performSearch();
    });



    function performSearch() {
        currentFilters.keyword = $('#searchInput').val().trim();
        loadPapers();
    }

    // 加载试卷列表
    function loadPapers() {
        var params = {
            resource_only: 'true'  // 只获取资源库试卷
        };
        if (currentFilters.keyword) {
            params.keyword = currentFilters.keyword;
        }

        $.get('/teacher/get_papers', params, function(data) {
            if (data.status === 'success') {
                renderPapers(data.papers);
                updatePaperCount(data.papers.length);
            } else {
                layer.msg('获取试卷列表失败: ' + data.message, {icon: 2});
            }
        }).fail(function() {
            layer.msg('网络错误，请稍后重试', {icon: 2});
        });
    }

    function renderPapers(papers) {
        var tbody = $('#paperTableBody');
        tbody.empty();

        if (papers.length === 0) {
            tbody.append(`
                <tr>
                    <td colspan="7" style="text-align: center; color: #999; padding: 40px;">
                        暂无试卷数据，点击"新建试卷"开始创建
                    </td>
                </tr>
            `);
            return;
        }

        papers.forEach(function(paper, index) {
            var createTime = new Date(paper.created_at).toLocaleDateString();
            var description = paper.description || '无描述';
            if (description.length > 30) {
                description = description.substring(0, 30) + '...';
            }

            // 类型标签样式
            var typeClass = paper.type === '试卷' ? 'layui-bg-blue' : 'layui-bg-green';
            var typeTag = `<span class="layui-badge ${typeClass}">${paper.type}</span>`;

            // 操作按钮
            var actions = `
                <a href="javascript:void(0)" onclick="previewPaper('${paper.id}')" class="action-btn view">
                    <i class="layui-icon layui-icon-eye"></i> 预览
                </a>
            `;

            // 只有作业才能查看分析，只有原始试卷才能删除
            if (paper.course_schedule_id) {
                actions += `
                    <a href="javascript:void(0)" onclick="viewAnalysis('${paper.id}')" class="action-btn analysis">
                        <i class="layui-icon layui-icon-chart"></i> 分析
                    </a>
                `;
            }

            if (!paper.course_schedule_id) {
                actions += `
                    <a href="javascript:void(0)" onclick="deletePaper('${paper.id}')" class="action-btn delete">
                        <i class="layui-icon layui-icon-delete"></i> 删除
                    </a>
                `;
            }

            var row = `
                <tr>
                    <td>${index + 1}</td>
                    <td>
                        <div style="font-weight: bold; margin-bottom: 4px;">${paper.title}</div>
                    </td>
                    <td>${typeTag}</td>
                    <td>${paper.source}</td>
                    <td>${description}</td>
                    <td>${createTime}</td>
                    <td>${actions}</td>
                </tr>
            `;
            tbody.append(row);
        });
    }

    function updatePaperCount(count) {
        $('#paperCountText').text(`共 ${count} 份试卷`);
    }

    // 删除试卷
    window.deletePaper = function(paperId) {
        layer.confirm('确定要删除这份试卷吗？删除后无法恢复！', {
            icon: 3,
            title: '确认删除'
        }, function(index) {
            $.ajax({
                url: '/teacher/paper/' + paperId,
                type: 'DELETE',
                success: function(data) {
                    if (data.status === 'success') {
                        layer.msg('删除成功', {icon: 1});
                        loadPapers(); // 重新加载列表
                    } else {
                        layer.msg('删除失败: ' + data.message, {icon: 2});
                    }
                },
                error: function() {
                    layer.msg('网络错误，请稍后重试', {icon: 2});
                }
            });
            layer.close(index);
        });
    };

    // 查看分析
    window.viewAnalysis = function(paperId) {
        window.open('/teacher/paper/analysis/' + paperId, '_blank');
    };

    // 预览试卷
    window.previewPaper = function(paperId) {
        layer.msg('正在加载试卷...', {icon: 16, shade: 0.3});

        $.get('/teacher/paper/' + paperId, function(data) {
            layer.closeAll();
            if (data.status === 'error') {
                layer.msg('加载失败: ' + data.message, {icon: 2});
                return;
            }

            // 构建预览内容
            let previewContent = buildPreviewContent(data);

            layer.open({
                type: 1,
                title: '试卷预览 - ' + data.title,
                area: ['50%', '80%'],
                content: previewContent,
                btn: ['关闭'],
                btnAlign: 'c'
            });
        }).fail(function() {
            layer.closeAll();
            layer.msg('网络错误，请稍后重试', {icon: 2});
        });
    };

    function buildPreviewContent(paperData) {
        let html = `
            <div style="padding: 20px; max-height: 70vh; overflow-y: auto;">
                <div style="text-align: center; margin-bottom: 30px; border-bottom: 2px solid #1E9FFF; padding-bottom: 20px;">
                    <h2 style="margin: 0; color: #333;">${paperData.title}</h2>
                    <p style="margin: 10px 0 0 0; color: #666;">${paperData.description || '无描述'}</p>
                    <div style="margin-top: 15px; color: #999; font-size: 14px;">
                        <span>考试时间: ${paperData.paper_time || 45} 分钟</span>
                        <span style="margin-left: 20px;">总分: ${paperData.questions ? paperData.questions.reduce((sum, q) => sum + (q.score || 0), 0) : 0} 分</span>
                    </div>
                </div>
                <div class="questions-preview">
        `;

        if (paperData.questions && paperData.questions.length > 0) {
            paperData.questions.forEach((question, index) => {
                html += `
                    <div style="margin-bottom: 25px; padding: 20px; border: 1px solid #e6e6e6; border-radius: 8px; background: #fafafa;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                            <h4 style="margin: 0; color: #333;">第${index + 1}题 (${question.score || 0}分)</h4>
                            <span style="background: #1E9FFF; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">
                                ${getQuestionTypeName(question.type)}
                            </span>
                        </div>
                        <div style="margin-bottom: 15px; line-height: 1.6;">
                            <strong>题目:</strong> ${question.desc || question.question || ''}
                        </div>
                `;

                // 根据题目类型显示选项
                if (question.type === 'single' || question.type === 'multiple') {
                    if (question.options && question.options.length > 0) {
                        html += '<div style="margin-bottom: 15px;"><strong>选项:</strong></div>';
                        question.options.forEach((option, optIndex) => {
                            const optionLabel = String.fromCharCode(65 + optIndex);
                            html += `<div style="margin: 8px 0; padding-left: 20px;">${optionLabel}. ${option}</div>`;
                        });
                    }
                }

                if (question.answer) {
                    html += `
                        <div style="margin-top: 15px; padding: 10px; background: #e8f5e8; border-left: 4px solid #4CAF50; border-radius: 4px;">
                            <strong style="color: #2E7D32;">参考答案:</strong> ${question.answer}
                        </div>
                    `;
                }

                html += '</div>';
            });
        } else {
            html += '<div style="text-align: center; color: #999; padding: 40px;">暂无题目</div>';
        }

        html += `
                </div>
            </div>
        `;

        return html;
    }

    function getQuestionTypeName(type) {
        const typeNames = {
            'single': '单选题',
            'multiple': '多选题',
            'judge': '判断题',
            'fill': '填空题',
            'text': '简答题'
        };
        return typeNames[type] || '未知';
    }

    // 页面加载时获取试卷列表
    loadPapers();
});
</script>
{% endblock %}
