{% extends "student/base.html" %}

{% block title %}我的作业{% endblock %}

{% block content %}
<div class="layui-card">
    <div class="layui-card-header">
        <h3>我的作业</h3>
    </div>
    <div class="layui-card-body">
        <table class="layui-table">
            <colgroup>
                <col width="25%">
                <col width="20%">
                <col width="15%">
                <col width="15%">
                <col>
            </colgroup>
            <thead>
                <tr>
                    <th>作业标题</th>
                    <th>所属课程</th>
                    <th>发布时间</th>
                    <th>状态</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                {% if homeworks %}
                    {% for hw in homeworks %}
                        <tr>
                            <td>{{ hw.title }}</td>
                            <td>{{ hw.course_name }}</td>
                            <td>{{ hw.created_at[:10] }}</td>
                            <td>
                                {% if hw.is_submitted %}
                                    <span class="layui-badge layui-bg-green">已提交</span>
                                {% else %}
                                    <span class="layui-badge layui-bg-orange">未提交</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if hw.is_submitted %}
                                    <button class="layui-btn layui-btn-xs layui-btn-disabled">已完成</button>
                                {% else %}
                                    <a href="{{ url_for('student.take_exam', exam_id=hw.id) }}" class="layui-btn layui-btn-xs">去做题</a>
                                {% endif %}
                            </td>
                        </tr>
                    {% endfor %}
                {% else %}
                    <tr>
                        <td colspan="5" style="text-align: center;">暂无作业</td>
                    </tr>
                {% endif %}
            </tbody>
        </table>
    </div>
</div>
{% endblock %} 