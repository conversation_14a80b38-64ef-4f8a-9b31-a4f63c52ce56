{% extends "student/base.html" %}

{% block title %}我的课程 - 智慧课堂系统{% endblock %}

{% block styles %}
<style>
    .course-card {
        margin-bottom: 20px;
        border: 1px solid #e6e6e6;
        border-radius: 5px;
        transition: box-shadow .3s;
    }
    .course-card:hover {
        box-shadow: 0 0 10px rgba(0,0,0,.1);
    }
    .course-card .layui-card-header {
        font-size: 16px;
        font-weight: bold;
    }
    .course-card .layui-card-body p {
        margin-bottom: 10px;
        color: #666;
    }
    .status {
        padding: 3px 10px;
        border-radius: 3px;
        color: #fff;
        font-size: 12px;
    }
    .status-in-progress { background-color: #1E9FFF; }
    .status-scheduled { background-color: #FFB800; }
    .status-completed { background-color: #999; }
</style>
{% endblock %}

{% block content %}
<div class="layui-row layui-col-space20">
    {% if courses %}
        {% for course in courses %}
        <div class="layui-col-md6 layui-col-lg4">
            <a href="{{ url_for('student.course_detail', course_id=course.id) }}">
                <div class="layui-card course-card">
                    <div class="layui-card-header">
                        {{ course.course_name }}
                        <span class="layui-badge layui-bg-blue layui-badge-rim">{{ course.course_code }}</span>
                    </div>
                    <div class="layui-card-body">
                        <p><i class="layui-icon layui-icon-user"></i> 教师: {{ course.teacher_name }}</p>
                        <p><i class="layui-icon layui-icon-location"></i> 地点: {{ course.classroom_name }}</p>
                        <p><i class="layui-icon layui-icon-log"></i> 班级: {{ course.class_name }}</p>
                        <p><i class="layui-icon layui-icon-date"></i> 时间: 星期{{ course.day_of_week }} {{ course.start_time }} - {{ course.end_time }}</p>
                        <p>
                            状态:
                            {% if course.status == 'in_progress' %}
                                <span class="status status-in-progress">进行中</span>
                            {% elif course.status == 'scheduled' %}
                                <span class="status status-scheduled">未开始</span>
                            {% else %}
                                <span class="status status-completed">已结束</span>
                            {% endif %}
                        </p>
                    </div>
                </div>
            </a>
        </div>
        {% endfor %}
    {% else %}
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-body">
                    <div class="layui-none">暂无课程安排</div>
                </div>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}