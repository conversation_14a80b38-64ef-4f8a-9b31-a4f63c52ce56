from flask import session
from flask_socketio import emit, join_room, leave_room
from app.models.database import get_db
import datetime
import uuid

def register_events(socketio):
    @socketio.on('connect')
    def handle_connect():
        """客户端连接事件"""
        # 可以在这里进行用户身份验证等操作
        # 简单起见，我们直接允许连接
        print('客户端已连接')
        emit('status', {'msg': '已连接至聊天服务器'})

    @socketio.on('disconnect')
    def handle_disconnect():
        """客户端断开连接事件"""
        print('客户端已断开')

    @socketio.on('join')
    def on_join(data):
        """加入房间"""
        # 根据用户类型获取用户名
        if session.get('user_type') == 'teacher':
            username = session.get('teacher_name', '匿名用户')
        elif session.get('user_type') == 'student':
            username = session.get('student_name', '匿名用户')
        else:
            username = '匿名用户'
            
        room = data.get('room', 'default_room')
        join_room(room)
        # print(f'用户 {username} 已加入房间 {room}')
        #emit('chat_message', {'user': '系统', 'message': f'{username} 加入了聊天室'}, to=room)

    @socketio.on('leave')
    def on_leave(data):
        """离开房间"""
        # 根据用户类型获取用户名
        if session.get('user_type') == 'teacher':
            username = session.get('teacher_name', '匿名用户')
        elif session.get('user_type') == 'student':
            username = session.get('student_name', '匿名用户')
        else:
            username = '匿名用户'
            
        room = data.get('room', 'default_room')
        leave_room(room)
        # print(f'用户 {username} 已离开房间 {room}')
        # emit('chat_message', {'user': '系统', 'message': f'{username} 离开了聊天室'}, to=room)

    @socketio.on('send_message')
    def handle_send_message(data):
        """接收并广播聊天消息，同时保存到弹幕数据库"""
        room = data.get('room', 'default_room')
        course_schedule_id = session.get('current_class_id')  # 从session中获取当前课堂ID
        print('session'==session)
        if not course_schedule_id:
            print("警告：未找到当前课程安排ID")
        
        # 根据用户类型获取用户信息
        if session.get('user_type') == 'teacher':
            username = session.get('teacher_name', '匿名用户')
            return  # 教师发送的消息不记录
        elif session.get('user_type') == 'student':
            username = session.get('student_name', '匿名用户')
            student_id = session.get('student_id')
        else:
            username = '匿名用户'
            return
        
        message_data = {
            'user': username,
            'message': data['message']
        }
        
        # 广播消息
        emit('chat_message', message_data, to=room)
        # print(f"房间 {room} 收到来自 {username} 的消息: {data['message']}")
        
        # 如果是学生且消息不为空，保存到弹幕表
        if student_id and data['message'].strip():
            try:
                db = get_db()
                current_time = datetime.datetime.now().isoformat()
                cursor = db.cursor()
                
                # 插入弹幕记录
                cursor.execute("""
                    INSERT INTO danmaku (id, course_schedule_id, student_id, content, created_at)
                    VALUES (?, ?, ?, ?, ?)
                """, (str(uuid.uuid4()), course_schedule_id, student_id, data['message'], current_time))
                
                db.commit()
            except Exception as e:
                print(f"保存弹幕失败: {str(e)}")
                db.rollback()
            finally:
                db.close()